/**
 * 文件拖拽上传模块 - 基于真实上传需求
 * <AUTHOR> Agent
 * @version 2.0.0
 */

// 上传配置常量
const UPLOAD_CONFIG = {
    SUPPORTED_EXTENSIONS: ['.txt'],
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    MAX_FILES_COUNT: 100,
    ALLOWED_MIME_TYPES: ['text/plain', 'text/txt', '']
};

// 上传状态枚举
const UPLOAD_STATUS = {
    PENDING: 'pending',
    UPLOADING: 'uploading',
    PARSING: 'parsing',
    COMPLETED: 'completed',
    ERROR: 'error'
};

/**
 * 拖拽上传管理器
 */
class DragUploadManager {
    constructor(options = {}) {
        this.options = {
            dropZoneSelector: '#drop-zone',
            fileInputSelector: '#file-input',
            progressContainerSelector: '#upload-progress',
            maxFiles: UPLOAD_CONFIG.MAX_FILES_COUNT,
            maxFileSize: UPLOAD_CONFIG.MAX_FILE_SIZE,
            ...options
        };

        this.state = {
            isUploading: false,
            uploadedFiles: [],
            currentProgress: 0,
            status: UPLOAD_STATUS.PENDING
        };

        this.callbacks = {
            onProgress: options.onProgress || null,
            onComplete: options.onComplete || null,
            onError: options.onError || null,
            onFileValidation: options.onFileValidation || null,
            onFileSelect: options.onFileSelect || null
        };

        console.log('[DragUpload] DragUploadManager initialized');
    }

    /**
     * 初始化拖拽上传
     */
    initialize() {
        try {
            // DOM绑定
            this.dropZone = document.querySelector(this.options.dropZoneSelector);
            this.fileInput = document.querySelector(this.options.fileInputSelector);
            this.progressContainer = document.querySelector(this.options.progressContainerSelector);

            if (!this.dropZone || !this.fileInput) {
                throw new Error('必需的DOM元素未找到');
            }

            // 绑定拖拽事件
            this.bindDragEvents();

            // 绑定文件输入事件
            this.bindFileInputEvents();

            console.log('[DragUpload] 拖拽上传初始化成功');
            return true;

        } catch (error) {
            console.error('[DragUpload] 初始化失败:', error);
            return false;
        }
    }

    /**
     * 兼容旧版本的init方法
     */
    init(dropZoneSelector, options = {}) {
        // 更新选项
        if (dropZoneSelector) {
            this.options.dropZoneSelector = dropZoneSelector;
        }
        this.callbacks = Object.assign(this.callbacks, options);

        return this.initialize();
    }

    /**
     * 绑定拖拽事件
     */
    bindDragEvents() {
        // 阻止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.dropZone.addEventListener(eventName, this.preventDefaults.bind(this));
            document.body.addEventListener(eventName, this.preventDefaults.bind(this));
        });

        // 拖拽进入高亮
        ['dragenter', 'dragover'].forEach(eventName => {
            this.dropZone.addEventListener(eventName, this.highlight.bind(this));
        });

        // 拖拽离开取消高亮
        ['dragleave', 'drop'].forEach(eventName => {
            this.dropZone.addEventListener(eventName, this.unhighlight.bind(this));
        });

        // 文件放置处理
        this.dropZone.addEventListener('drop', this.handleDrop.bind(this));

        // 点击触发文件选择
        this.dropZone.addEventListener('click', () => {
            this.fileInput.click();
        });
    }

    /**
     * 绑定文件输入事件
     */
    bindFileInputEvents() {
        this.fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFiles(files);
        });
    }

    /**
     * 阻止默认行为
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * 高亮拖拽区域
     */
    highlight(element) {
        element.classList.add('drag-over');
    }

    /**
     * 取消高亮
     */
    unhighlight(element) {
        element.classList.remove('drag-over');
    }

    /**
     * 处理文件放置
     */
    handleDrop(e) {
        try {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            this.handleFiles(files);
            
        } catch (error) {
            console.error(`[DragUpload] Drop failed: ${error.message}`);
        }
    }

    /**
     * 处理文件列表
     */
    handleFiles(files) {
        const fileArray = Array.from(files);
        console.log(`[DragUpload] Processing ${fileArray.length} files`);
        
        fileArray.forEach(file => {
            if (this.validateFile(file)) {
                this.uploadedFiles.push(file);
                
                if (this.callbacks.onFileSelect) {
                    this.callbacks.onFileSelect(file);
                }
            }
        });
    }

    /**
     * 验证文件
     */
    validateFile(file) {
        try {
            // 检查文件类型
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!this.config.ACCEPTED_TYPES.includes(fileExtension)) {
                console.warn(`[DragUpload] Invalid file type: ${fileExtension}`);
                return false;
            }

            // 检查文件大小
            if (file.size > this.config.MAX_FILE_SIZE) {
                console.warn(`[DragUpload] File too large: ${file.size} bytes`);
                return false;
            }

            console.log(`[DragUpload] File validated: ${file.name}`);
            return true;
            
        } catch (error) {
            console.error(`[DragUpload] Validation failed: ${error.message}`);
            return false;
        }
    }

    /**
     * 读取文件内容
     */
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                resolve({
                    name: file.name,
                    size: file.size,
                    content: e.target.result,
                    lastModified: file.lastModified
                });
            };
            
            reader.onerror = () => {
                reject(new Error(`Failed to read file: ${file.name}`));
            };
            
            reader.readAsText(file, 'UTF-8');
        });
    }

    /**
     * 获取上传的文件列表
     */
    getUploadedFiles() {
        return this.uploadedFiles;
    }

    /**
     * 清空文件列表
     */
    clearFiles() {
        this.uploadedFiles = [];
        console.log('[DragUpload] Files cleared');
    }

    /**
     * 移除指定文件
     */
    removeFile(fileName) {
        const index = this.uploadedFiles.findIndex(file => file.name === fileName);
        if (index > -1) {
            this.uploadedFiles.splice(index, 1);
            console.log(`[DragUpload] File removed: ${fileName}`);
            return true;
        }
        return false;
    }
}

// 创建全局实例
const dragUploadManager = new DragUploadManager();

// 导出
export default dragUploadManager;
export { DragUploadManager };
