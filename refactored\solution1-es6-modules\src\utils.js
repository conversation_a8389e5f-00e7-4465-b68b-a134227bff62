/**
 * ==================== GoMyHire 对话分析系统 - 工具函数库 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Utility Functions Library
 * 版本: 2.0.0
 * 功能描述: 提供系统核心工具函数集合，包括日期处理、字符串处理、数据验证、文件处理、错误处理、DOM操作等
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: 无 (基础工具模块)
 * 间接依赖: 无
 * 被依赖: 几乎所有模块 (service-container.js, event-bus.js, main.js, ui.js, parser.js, storage.js 等)
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 0 (第一层，基础模块)
 * 加载时机: core (核心模块，立即加载)
 * 加载条件: 无条件加载
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 日期时间工具 (formatDateTime, calculateTimeDiff)
 *   - 字符串处理 (truncateString, cleanText)
 *   - 数据验证 (isValidEmail, isValidPhone)
 *   - 文件处理 (getFileExtension, formatFileSize, isValidFileType)
 *   - 错误处理 (safeExecute, createError)
 *   - 数组处理 (uniqueArray, chunkArray)
 *   - 哈希和ID生成 (simpleHash, generateUniqueId)
 *   - 文本相似度计算 (calculateTextSimilarity)
 *   - 防抖和节流 (debounce, throttle)
 *   - DOM操作 (updateElementText, safeQuerySelector)
 *   - 数据格式化 (formatScore, formatPercentage)
 *   - 本地存储 (safeLocalStorageSet/Get/Remove)
 *   - 网络和异步工具 (waitForOnline, executeWithTimeout)
 * 导出接口: 33个工具函数
 * 全局注册: window.ModuleExports['utils.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 各类原始数据 (字符串、数字、对象、数组、DOM元素等)
 * 输出数据: 处理后的数据 (格式化、验证、转换结果)
 * 状态管理: 无状态 (纯函数工具集)
 * 事件处理: DOM事件处理辅助函数
 * 
 * @INTEGRATION (集成关系)
 * UI集成: 提供DOM操作、事件处理、数据格式化工具
 * 服务集成: 提供错误处理、异步工具、网络状态检测
 * 存储集成: 提供安全的LocalStorage操作接口
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (包含多个工具函数)
 * 加载性能: 快速 (纯函数定义，无复杂计算)
 * 运行时性能: 优秀 (纯函数，无副作用，可缓存结果)
 */

// ==================== 日期时间工具函数 ====================
/**
 * 格式化日期时间
 * @UTIL 日期时间格式化工具
 * @param {Date} date - 要格式化的日期对象
 * @param {string} format - 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
function formatDateTime(date = new Date(), format = 'YYYY-MM-DD HH:mm:ss') {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 计算时间差
 * @UTIL 时间差计算工具
 * @param {Date} startTime - 开始时间
 * @param {Date} endTime - 结束时间，默认为当前时间
 * @returns {Object} 包含天、小时、分钟、秒的时间差对象
 */
function calculateTimeDiff(startTime, endTime = new Date()) {
    const diffMs = endTime - startTime;
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds, totalMs: diffMs };
}

// ==================== 字符串处理工具函数 ====================
/**
 * 安全的字符串截取
 * @SHARED_UTIL 字符串截取工具
 * @param {string} str - 要截取的字符串
 * @param {number} maxLength - 最大长度
 * @param {string} suffix - 截取后的后缀，默认为'...'
 * @returns {string} 截取后的字符串
 */
function truncateString(str, maxLength, suffix = '...') {
    if (!str || typeof str !== 'string') return '';
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * 清理和标准化文本
 * @SHARED_UTIL 文本清理工具
 * @param {string} text - 要清理的文本
 * @returns {string} 清理后的文本
 */
function cleanText(text) {
    if (!text || typeof text !== 'string') return '';
    return text
        .trim()                          // 移除首尾空白
        .replace(/\s+/g, ' ')           // 合并多个空白字符
        .replace(/[\r\n]+/g, ' ')       // 替换换行符为空格
        .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文、英文、数字和空格
        .trim();
}

// ==================== 数据验证工具函数 ====================
/**
 * 验证邮箱格式
 * @UTIL 邮箱验证工具
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // @SHARED_CONSTANT 邮箱验证正则
    return emailRegex.test(email);
}

/**
 * 验证手机号格式
 * @UTIL 手机号验证工具
 * @param {string} phone - 手机号码
 * @returns {boolean} 是否为有效手机号
 */
function isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/; // @SHARED_CONSTANT 手机号验证正则
    return phoneRegex.test(phone);
}

/**
 * 深度克隆对象
 * @SHARED_UTIL 对象深拷贝工具
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }
    return obj;
}

// ==================== 文件处理工具函数 ====================
/**
 * 获取文件扩展名
 * @UTIL 文件扩展名提取工具
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名（小写）
 */
function getFileExtension(filename) {
    if (!filename || typeof filename !== 'string') return '';
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
}

/**
 * 格式化文件大小
 * @UTIL 文件大小格式化工具
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']; // @SHARED_CONSTANT 文件大小单位
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 验证文件类型
 * @UTIL 文件类型验证工具
 * @param {File} file - 文件对象
 * @param {Array} allowedTypes - 允许的文件类型数组
 * @returns {boolean} 是否为允许的文件类型
 */
function isValidFileType(file, allowedTypes = ['txt', 'json', 'csv']) {
    if (!file || !file.name) return false;
    const extension = getFileExtension(file.name);
    return allowedTypes.includes(extension);
}

// ==================== 错误处理工具函数 ====================
/**
 * 安全执行函数
 * @UTIL 错误捕获执行工具
 * @param {Function} fn - 要执行的函数
 * @param {any} defaultValue - 出错时的默认返回值
 * @param {Function} errorHandler - 错误处理函数
 * @returns {any} 函数执行结果或默认值
 */
function safeExecute(fn, defaultValue = null, errorHandler = null) {
    try {
        return fn();
    } catch (error) {
        console.error('Safe execute error:', error);
        if (errorHandler && typeof errorHandler === 'function') {
            errorHandler(error);
        }
        return defaultValue;
    }
}

/**
 * 创建错误对象
 * @FACTORY 标准错误对象创建工厂
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {any} details - 错误详情
 * @returns {Object} 标准错误对象
 */
function createError(message, code = 'UNKNOWN_ERROR', details = null) {
    return {
        message,
        code,
        details,
        timestamp: new Date().toISOString(),
        stack: new Error().stack
    };
}

// ==================== 数组处理工具函数 ====================
/**
 * 数组去重
 * @UTIL 数组去重工具
 * @param {Array} array - 要去重的数组
 * @param {string} key - 对象数组的去重键名
 * @returns {Array} 去重后的数组
 */
function uniqueArray(array, key = null) {
    if (!Array.isArray(array)) return [];
    if (key) {
        const seen = new Set();
        return array.filter(item => {
            const value = item[key];
            if (seen.has(value)) return false;
            seen.add(value);
            return true;
        });
    }
    return [...new Set(array)];
}

/**
 * 数组分块
 * @UTIL 数组分块工具
 * @param {Array} array - 要分块的数组
 * @param {number} size - 每块的大小
 * @returns {Array} 分块后的二维数组
 */
function chunkArray(array, size) {
    if (!Array.isArray(array) || size <= 0) return [];
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

// ==================== 哈希和ID生成工具函数 ====================
/**
 * 简单哈希函数
 * @UTIL 简单哈希工具
 * @param {string} str - 要哈希的字符串
 * @returns {string} 哈希值
 */
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
}

/**
 * 生成唯一ID
 * @FACTORY ID生成工厂
 * @param {string} prefix - ID前缀
 * @returns {string} 唯一ID
 */
function generateUniqueId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// ==================== 文本相似度计算工具函数 ====================
/**
 * 计算文本相似度
 * @UTIL 文本相似度计算工具
 * @param {string} text1 - 第一个文本
 * @param {string} text2 - 第二个文本
 * @returns {number} 相似度分数（0-1）
 */
function calculateTextSimilarity(text1, text2) {
    if (!text1 || !text2) return 0;
    
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
}

// ==================== 防抖和节流工具函数 ====================
/**
 * 防抖函数
 * @TIMER 防抖工具
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @TIMER 节流工具
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ==================== DOM操作工具函数 ====================
/**
 * 更新元素文本内容
 * @UTIL DOM文本更新工具
 * @param {string} elementId - 元素ID
 * @param {string|number} text - 要设置的文本
 */
function updateElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    }
}

/**
 * 安全获取DOM元素
 * @UTIL DOM元素获取工具
 * @param {string} selector - CSS选择器
 * @param {Element} parent - 父元素，默认为document
 * @returns {Element|null} DOM元素或null
 */
function safeQuerySelector(selector, parent = document) {
    try {
        return parent.querySelector(selector);
    } catch (error) {
        console.warn('Invalid selector:', selector, error);
        return null;
    }
}

/**
 * 安全获取多个DOM元素
 * @UTIL DOM元素列表获取工具
 * @param {string} selector - CSS选择器
 * @param {Element} parent - 父元素，默认为document
 * @returns {NodeList} DOM元素列表
 */
function safeQuerySelectorAll(selector, parent = document) {
    try {
        return parent.querySelectorAll(selector);
    } catch (error) {
        console.warn('Invalid selector:', selector, error);
        return [];
    }
}

// ==================== 数据格式化工具函数 ====================
/**
 * 格式化分数显示
 * @UTIL 分数格式化工具
 * @param {number} score - 分数
 * @returns {string} 格式化后的分数HTML
 */
function formatScore(score) {
    if (typeof score === 'number') {
        const cssClass = score >= 80 ? 'score-high' : score >= 60 ? 'score-medium' : 'score-low';
        return `<span class="${cssClass}">${score}%</span>`;
    }
    return score;
}

/**
 * 格式化响应时间
 * @UTIL 响应时间格式化工具
 * @param {number} time - 时间（秒）
 * @returns {string} 格式化后的时间
 */
function formatResponseTime(time) {
    if (typeof time === 'number') {
        return time < 60 ? `${time}秒` : `${Math.round(time/60)}分钟`;
    }
    return time;
}

/**
 * 格式化百分比
 * @UTIL 百分比格式化工具
 * @param {number} value - 数值
 * @param {number} total - 总数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的百分比
 */
function formatPercentage(value, total, decimals = 1) {
    if (!total || total === 0) return '0%';
    const percentage = ((value / total) * 100).toFixed(decimals);
    return `${percentage}%`;
}

// ==================== 数据处理工具函数 ====================
/**
 * 合并数据源
 * @UTIL 数据源合并工具
 * @param {Array} existingSources - 现有数据源
 * @param {Array} newSources - 新数据源
 * @returns {Array} 合并后的数据源（去重）
 */
function mergeSources(existingSources = [], newSources = []) {
    const allSources = [...existingSources, ...newSources];
    return [...new Set(allSources)]; // 去重
}

/**
 * 提取关键词
 * @UTIL 关键词提取工具
 * @param {string} code - 代码或文本
 * @returns {Array} 关键词数组
 */
function extractKeywords(code) {
    const keywords = [];
    const patterns = [
        /\b(if|else|for|while|switch|case|return|throw|try|catch)\b/g,
        /\b(console\.log|console\.error|console\.warn)\b/g,
        /\b(document\.|window\.|localStorage\.)\w+/g
    ];

    patterns.forEach(pattern => {
        const matches = code.match(pattern) || [];
        keywords.push(...matches);
    });

    return [...new Set(keywords)]; // 去重
}

/**
 * 计算相似度
 * @UTIL 相似度计算工具
 * @param {Object} sig1 - 第一个签名对象
 * @param {Object} sig2 - 第二个签名对象
 * @returns {number} 相似度分数（0-1）
 */
function calculateSimilarity(sig1, sig2) {
    let score = 0;
    if (sig1.paramCount === sig2.paramCount) score += 0.3;
    const commonKeywords = sig1.keywords.filter(k => sig2.keywords.includes(k));
    const keywordSimilarity = commonKeywords.length / Math.max(sig1.keywords.length, sig2.keywords.length, 1);
    score += keywordSimilarity * 0.5;
    const lengthRatio = Math.min(sig1.length, sig2.length) / Math.max(sig1.length, sig2.length);
    score += lengthRatio * 0.2;
    return score / 3;
}

// ==================== 网络和异步工具函数 ====================
/**
 * 等待网络连接
 * @UTIL 网络连接等待工具
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} Promise对象
 */
function waitForOnline(timeout = 30000) {
    if (navigator.onLine) {
        return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
            window.removeEventListener('online', onOnline);
            reject(new Error('网络连接超时'));
        }, timeout);

        const onOnline = () => {
            clearTimeout(timeoutId);
            window.removeEventListener('online', onOnline);
            resolve();
        };

        window.addEventListener('online', onOnline);
    });
}

/**
 * 超时执行包装器
 * @UTIL 超时执行工具
 * @param {Function} fn - 要执行的函数
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} Promise对象
 */
function executeWithTimeout(fn, timeout) {
    return Promise.race([
        Promise.resolve(fn()),
        new Promise((_, reject) =>
            setTimeout(() => reject(new Error(`Handler timeout after ${timeout}ms`)), timeout)
        )
    ]);
}

// ==================== 本地存储工具函数 ====================
/**
 * 安全的本地存储设置
 * @UTIL 本地存储设置工具
 * @param {string} key - 存储键
 * @param {any} value - 存储值
 * @returns {boolean} 是否设置成功
 */
function safeLocalStorageSet(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.warn('LocalStorage set failed:', error);
        return false;
    }
}

/**
 * 安全的本地存储获取
 * @UTIL 本地存储获取工具
 * @param {string} key - 存储键
 * @param {any} defaultValue - 默认值
 * @returns {any} 存储的值或默认值
 */
function safeLocalStorageGet(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.warn('LocalStorage get failed:', error);
        return defaultValue;
    }
}

/**
 * 安全的本地存储删除
 * @UTIL 本地存储删除工具
 * @param {string} key - 存储键
 * @returns {boolean} 是否删除成功
 */
function safeLocalStorageRemove(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.warn('LocalStorage remove failed:', error);
        return false;
    }
}

// 将所有工具函数导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['utils.js'] = {
    formatDateTime,
    calculateTimeDiff,
    truncateString,
    cleanText,
    isValidEmail,
    isValidPhone,
    deepClone,
    getFileExtension,
    formatFileSize,
    isValidFileType,
    safeExecute,
    createError,
    uniqueArray,
    chunkArray,
    simpleHash,
    generateUniqueId,
    calculateTextSimilarity,
    debounce,
    throttle,
    updateElementText,
    safeQuerySelector,
    safeQuerySelectorAll,
    formatScore,
    formatResponseTime,
    formatPercentage,
    mergeSources,
    extractKeywords,
    calculateSimilarity,
    waitForOnline,
    executeWithTimeout,
    safeLocalStorageSet,
    safeLocalStorageGet,
    safeLocalStorageRemove
};
