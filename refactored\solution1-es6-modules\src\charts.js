/**
 * 图表管理模块
 */

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.config = window.CONSTANTS?.CHART_CONFIG || {
            DEFAULT_HEIGHT: 400,
            COLORS: {
                PRIMARY: '#007bff',
                SUCCESS: '#28a745',
                WARNING: '#ffc107',
                DANGER: '#dc3545',
                INFO: '#17a2b8'
            }
        };

        this.chartConfigs = this.createChartConfigs();
    }

    /**
     * 创建图表配置模板
     */
    createChartConfigs() {
        return {
            questions: {
                title: { text: '司机问题类型分布', left: 'center' },
                tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                legend: { orient: 'vertical', left: 'left' },
                series: [{
                    name: '问题类型',
                    type: 'pie',
                    radius: '50%',
                    data: [],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0,0,0,0.5)'
                        }
                    }
                }]
            },
            effectiveness: {
                title: { text: '客服有效性评分分布', left: 'center' },
                tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                xAxis: { type: 'category', data: ['1分', '2分', '3分', '4分', '5分'] },
                yAxis: { type: 'value', name: '数量' },
                series: [{
                    name: '有效性评分',
                    type: 'bar',
                    data: [],
                    itemStyle: { color: this.config.COLORS.SUCCESS }
                }]
            },
            satisfaction: {
                title: { text: '客户满意度分布', left: 'center' },
                tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                legend: { orient: 'horizontal', bottom: 0 },
                series: [{
                    name: '满意度',
                    type: 'pie',
                    radius: ['30%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: { show: false, position: 'center' },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '30',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: { show: false },
                    data: []
                }]
            },
            knowledge: {
                title: { text: '知识库覆盖率统计', left: 'center' },
                tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                xAxis: { type: 'category', data: ['常见问题数', '解决方案', '待完善', '新问题'] },
                yAxis: { type: 'value', name: '数量' },
                series: [{
                    name: '知识库统计',
                    type: 'bar',
                    data: [],
                    itemStyle: { color: this.config.COLORS.WARNING }
                }]
            }
        };
    }

    /**
     * 初始化图表
     */
    initChart(containerId, chartType = 'bar') {
        try {
            const container = document.getElementById(containerId);
            if (!container) {
                throw new Error(`Container not found: ${containerId}`);
            }

            if (typeof echarts === 'undefined') {
                this.createFallbackChart(container, chartType);
                return null;
            }

            const chart = echarts.init(container);
            this.charts.set(containerId, chart);

            const configKey = containerId.replace('chart-', '');
            const config = this.chartConfigs[configKey];

            if (config) {
                chart.setOption(config);
            } else {
                const defaultConfig = this.buildDefaultChartOption(chartType);
                chart.setOption(defaultConfig);
            }

            return chart;

        } catch (error) {
            console.error(`[Charts] Init failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * 构建默认图表配置
     */
    buildDefaultChartOption(chartType) {
        const baseOption = {
            title: { text: '数据统计', left: 'center' },
            tooltip: { trigger: 'axis' },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            }
        };

        switch (chartType) {
            case 'pie':
                return {
                    ...baseOption,
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                    series: [{
                        name: '数据',
                        type: 'pie',
                        radius: '50%',
                        data: [],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0,0,0,0.5)'
                            }
                        }
                    }]
                };
            case 'line':
                return {
                    ...baseOption,
                    xAxis: { type: 'category', data: [] },
                    yAxis: { type: 'value' },
                    series: [{
                        name: '数据',
                        type: 'line',
                        data: [],
                        smooth: true
                    }]
                };
            default: // bar
                return {
                    ...baseOption,
                    xAxis: { type: 'category', data: [] },
                    yAxis: { type: 'value' },
                    series: [{
                        name: '数据',
                        type: 'bar',
                        data: [],
                        itemStyle: { color: this.config.COLORS.PRIMARY }
                    }]
                };
        }
    }

    /**
     * 更新图表数据
     */
    updateChart(containerId, data, options = {}) {
        try {
            const chart = this.charts.get(containerId);
            if (!chart) {
                console.warn(`[Charts] Chart not found: ${containerId}`);
                return;
            }

            // 根据容器ID确定更新方式
            const configKey = containerId.replace('chart-', '');

            switch (configKey) {
                case 'questions':
                    this.updateQuestionsChart(chart, data);
                    break;
                case 'effectiveness':
                    this.updateEffectivenessChart(chart, data);
                    break;
                case 'satisfaction':
                    this.updateSatisfactionChart(chart, data);
                    break;
                case 'knowledge':
                    this.updateKnowledgeChart(chart, data);
                    break;
                default:
                    const chartOption = this.buildGenericChartOption(data, options);
                    chart.setOption(chartOption, true);
            }

        } catch (error) {
            console.error(`[Charts] Update failed: ${error.message}`);
        }
    }

    /**
     * 更新问题分布图表
     */
    updateQuestionsChart(chart, data) {
        const questionStats = data.questionCategories || {};
        const chartData = Object.entries(questionStats).map(([category, count]) => ({
            name: category,
            value: count
        }));

        chart.setOption({
            series: [{
                data: chartData
            }]
        }, true);
    }

    /**
     * 更新有效性评分图表
     */
    updateEffectivenessChart(chart, data) {
        const effectivenessStats = [0, 0, 0, 0, 0]; // 1-5分统计

        if (data.detailedQuestions) {
            data.detailedQuestions.forEach(item => {
                const score = item.effectiveness || 3;
                if (score >= 1 && score <= 5) {
                    effectivenessStats[score - 1]++;
                }
            });
        }

        chart.setOption({
            series: [{
                data: effectivenessStats
            }]
        }, true);
    }

    /**
     * 更新满意度图表
     */
    updateSatisfactionChart(chart, data) {
        const satisfactionStats = [0, 0, 0, 0, 0]; // 1-5分统计

        if (data.detailedQuestions) {
            data.detailedQuestions.forEach(item => {
                const score = item.satisfaction || 3;
                if (score >= 1 && score <= 5) {
                    satisfactionStats[score - 1]++;
                }
            });
        }

        const chartData = satisfactionStats.map((count, index) => ({
            name: `${index + 1}分`,
            value: count
        }));

        chart.setOption({
            series: [{
                data: chartData
            }]
        }, true);
    }

    /**
     * 更新知识库图表
     */
    updateKnowledgeChart(chart, data) {
        const stats = {
            commonQuestions: data.qaDataset ? data.qaDataset.filter(qa => qa.isCommon).length : 0,
            solutions: data.qaDataset ? data.qaDataset.filter(qa => qa.answer && qa.answer.length > 0).length : 0,
            pending: data.detailedQuestions ? data.detailedQuestions.filter(q => !q.resolved).length : 0,
            newIssues: data.questionCategories ? Object.keys(data.questionCategories).length : 0
        };

        const chartData = [
            stats.commonQuestions,
            stats.solutions,
            stats.pending,
            stats.newIssues
        ];

        chart.setOption({
            series: [{
                data: chartData
            }]
        }, true);
    }

    /**
     * 构建通用图表配置
     */
    buildGenericChartOption(data, options) {
        const defaultOption = {
            title: {
                text: options.title || '数据统计',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: data.categories || []
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: data.values || [],
                type: options.type || 'bar',
                itemStyle: {
                    color: this.config.COLORS.PRIMARY
                }
            }]
        };

        return Object.assign(defaultOption, options.customOption || {});
    }

    /**
     * 批量更新所有图表
     */
    updateAllCharts(analysisData) {
        try {
            if (!analysisData) {
                console.warn('[Charts] No analysis data provided');
                return;
            }

            // 更新所有已初始化的图表
            for (const [containerId, chart] of this.charts) {
                this.updateChart(containerId, analysisData);
            }

            console.log('[Charts] All charts updated successfully');

        } catch (error) {
            console.error('[Charts] Failed to update all charts:', error);
        }
    }

    /**
     * 重置图表数据
     */
    resetChart(containerId) {
        try {
            const chart = this.charts.get(containerId);
            if (!chart) {
                console.warn(`[Charts] Chart not found: ${containerId}`);
                return;
            }

            const configKey = containerId.replace('chart-', '');
            const config = this.chartConfigs[configKey];

            if (config) {
                chart.setOption(config, true);
            }

            console.log(`[Charts] Chart reset: ${containerId}`);

        } catch (error) {
            console.error(`[Charts] Reset failed: ${error.message}`);
        }
    }

    /**
     * 重置所有图表
     */
    resetAllCharts() {
        for (const [containerId] of this.charts) {
            this.resetChart(containerId);
        }
        console.log('[Charts] All charts reset');
    }

    /**
     * 导出图表为图片
     */
    exportChart(containerId, filename = null) {
        try {
            const chart = this.charts.get(containerId);
            if (!chart) {
                console.warn(`[Charts] Chart not found: ${containerId}`);
                return null;
            }

            const dataURL = chart.getDataURL({
                type: 'png',
                pixelRatio: 2,
                backgroundColor: '#fff'
            });

            if (filename) {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = filename;
                link.href = dataURL;
                link.click();
            }

            console.log(`[Charts] Chart exported: ${containerId}`);
            return dataURL;

        } catch (error) {
            console.error(`[Charts] Export failed: ${error.message}`);
            return null;
        }
    }

    /**
     * 创建备用图表（当ECharts不可用时）
     */
    createFallbackChart(container, type) {
        container.innerHTML = `
            <div style="padding: 20px; text-align: center; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
                <i class="fas fa-chart-${type === 'pie' ? 'pie' : 'bar'}" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>
                <p style="color: #666; margin: 0; font-size: 16px;">图表加载中...</p>
                <small style="color: #999;">ECharts库正在加载，请稍候</small>
                <div style="margin-top: 10px;">
                    <button onclick="location.reload()" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">重新加载</button>
                </div>
            </div>
        `;
    }

    /**
     * 检查ECharts可用性
     */
    checkEChartsAvailability() {
        return typeof echarts !== 'undefined';
    }

    /**
     * 等待ECharts加载
     */
    waitForECharts(timeout = 10000) {
        return new Promise((resolve, reject) => {
            if (this.checkEChartsAvailability()) {
                resolve();
                return;
            }

            const checkInterval = setInterval(() => {
                if (this.checkEChartsAvailability()) {
                    clearInterval(checkInterval);
                    clearTimeout(timeoutHandle);
                    resolve();
                }
            }, 100);

            const timeoutHandle = setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error('ECharts loading timeout'));
            }, timeout);
        });
    }

    /**
     * 销毁图表
     */
    destroyChart(containerId) {
        try {
            const chart = this.charts.get(containerId);
            if (chart) {
                chart.dispose();
                this.charts.delete(containerId);
                console.log(`[Charts] Chart destroyed: ${containerId}`);
            }
        } catch (error) {
            console.error(`[Charts] Destroy failed: ${error.message}`);
        }
    }

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        for (const [containerId] of this.charts) {
            this.destroyChart(containerId);
        }
        console.log('[Charts] All charts destroyed');
    }

    /**
     * 获取图表实例
     */
    getChart(containerId) {
        return this.charts.get(containerId);
    }

    /**
     * 获取所有图表实例
     */
    getAllCharts() {
        return new Map(this.charts);
    }

    /**
     * 获取图表统计信息
     */
    getChartsInfo() {
        return {
            totalCharts: this.charts.size,
            chartIds: Array.from(this.charts.keys()),
            echartsAvailable: this.checkEChartsAvailability()
        };
    }
}
}

// 创建全局实例
const chartManager = new ChartManager();

// 导出
// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['charts.js'] = { 
    ChartManager, 
    chartManager // 兼容性导出
};
