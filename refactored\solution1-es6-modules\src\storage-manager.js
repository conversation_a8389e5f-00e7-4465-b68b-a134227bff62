/**
 * ==================== GoMyHire 对话分析系统 - 存储管理器 ====================
 * @SERVICE 从standalone.html完整迁移的存储管理系统
 * 实现localStorage的封装和管理，包括数据序列化、存储空间检查、数据清理等功能
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// ==================== 基础存储管理器类 ====================
/**
 * 基础存储管理器类 - 负责LocalStorage的读写操作和数据序列化
 * @SERVICE 数据持久化存储管理
 */
class StorageManager {
    constructor() {
        this.prefix = 'gomyhire_';  // 存储键前缀，避免命名冲突
    }

    /**
     * 保存数据到LocalStorage
     * @SERVICE 数据保存服务
     * @param {string} key - 存储键名
     * @param {any} data - 要保存的数据
     * @returns {boolean} 保存是否成功
     */
    save(key, data) {
        try {
            const serializedData = JSON.stringify(data);
            localStorage.setItem(this.prefix + key, serializedData);
            console.log(`💾 [存储] 数据已保存: ${key} (${getUtils()?.formatFileSize(serializedData.length) || serializedData.length + ' chars'})`);
            return true;
        } catch (error) {
            console.error(`❌ [存储] 保存失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 从LocalStorage加载数据
     * @SERVICE 数据加载服务
     * @param {string} key - 存储键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 加载的数据或默认值
     */
    load(key, defaultValue = null) {
        try {
            const serializedData = localStorage.getItem(this.prefix + key);
            if (serializedData === null) {
                return defaultValue;
            }
            const data = JSON.parse(serializedData);
            console.log(`📂 [存储] 数据已加载: ${key} (${getUtils()?.formatFileSize(serializedData.length) || serializedData.length + ' chars'})`);
            return data;
        } catch (error) {
            console.error(`❌ [存储] 加载失败: ${key}`, error);
            return defaultValue;
        }
    }

    /**
     * 删除存储的数据
     * @SERVICE 数据删除服务
     * @param {string} key - 存储键名
     * @returns {boolean} 删除是否成功
     */
    remove(key) {
        try {
            localStorage.removeItem(this.prefix + key);
            console.log(`🗑️ [存储] 数据已删除: ${key}`);
            return true;
        } catch (error) {
            console.error(`❌ [存储] 删除失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 检查键是否存在
     * @SERVICE 存在性检查服务
     * @param {string} key - 存储键名
     * @returns {boolean} 键是否存在
     */
    exists(key) {
        return localStorage.getItem(this.prefix + key) !== null;
    }

    /**
     * 获取所有应用相关的键
     * @SERVICE 键列表获取服务
     * @returns {Array} 键名数组
     */
    getAllKeys() {
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                keys.push(key.substring(this.prefix.length));
            }
        }
        return keys;
    }

    /**
     * 清空所有应用数据
     * @SERVICE 数据清空服务
     * @returns {number} 清空的项目数量
     */
    clear() {
        const keys = this.getAllKeys();
        keys.forEach(key => this.remove(key));
        console.log(`🧹 [存储] 已清空 ${keys.length} 个数据项`);
        return keys.length;
    }

    /**
     * 获取存储使用情况
     * @SERVICE 存储状态查询服务
     * @returns {Object} 存储使用情况统计
     */
    getStorageInfo() {
        try {
            const keys = Object.keys(localStorage);
            const appKeys = keys.filter(key => key.startsWith(this.prefix));
            let totalSize = 0;

            appKeys.forEach(key => {
                totalSize += localStorage.getItem(key).length;
            });

            return {
                totalKeys: appKeys.length,
                totalSize: totalSize,
                formattedSize: getUtils()?.formatFileSize(totalSize) || totalSize + ' bytes',
                availableSpace: this.getAvailableSpace()
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return { totalKeys: 0, totalSize: 0, formattedSize: '0 B', availableSpace: 0 };
        }
    }

    /**
     * 检查存储空间是否足够
     * @SERVICE 存储空间检查服务
     * @param {number} requiredSize - 需要的空间大小（字节）
     * @returns {boolean} 空间是否足够
     */
    hasEnoughSpace(requiredSize) {
        try {
            // 尝试存储测试数据
            const testKey = this.prefix + 'space_test';
            const testData = 'x'.repeat(requiredSize);
            localStorage.setItem(testKey, testData);
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.warn(`⚠️ [存储] 存储空间不足: ${getUtils()?.formatFileSize(requiredSize) || requiredSize + ' bytes'}`);
            return false;
        }
    }

    /**
     * 获取可用存储空间（估算）
     * @UTIL 可用空间估算工具
     * @returns {number} 估算的可用空间（字节）
     */
    getAvailableSpace() {
        try {
            let testSize = 1024; // 从1KB开始测试
            let maxSize = 0;

            while (testSize <= 10 * 1024 * 1024) { // 最大测试10MB
                try {
                    const testKey = this.prefix + 'space_test';
                    const testData = 'x'.repeat(testSize);
                    localStorage.setItem(testKey, testData);
                    localStorage.removeItem(testKey);
                    maxSize = testSize;
                    testSize *= 2;
                } catch (error) {
                    break;
                }
            }

            return maxSize;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 批量保存数据
     * @SERVICE 批量数据保存服务
     * @param {Object} dataMap - 键值对映射
     * @returns {Object} 保存结果统计
     */
    saveBatch(dataMap) {
        const results = { success: 0, failed: 0, errors: [] };

        for (const [key, data] of Object.entries(dataMap)) {
            if (this.save(key, data)) {
                results.success++;
            } else {
                results.failed++;
                results.errors.push(key);
            }
        }

        console.log(`📦 [存储] 批量保存完成: 成功 ${results.success}, 失败 ${results.failed}`);
        return results;
    }

    /**
     * 批量加载数据
     * @SERVICE 批量数据加载服务
     * @param {Array} keys - 键名数组
     * @param {any} defaultValue - 默认值
     * @returns {Object} 加载的数据映射
     */
    loadBatch(keys, defaultValue = null) {
        const results = {};

        keys.forEach(key => {
            results[key] = this.load(key, defaultValue);
        });

        console.log(`📂 [存储] 批量加载完成: ${keys.length} 个键`);
        return results;
    }

    /**
     * 数据备份
     * @SERVICE 数据备份服务
     * @returns {Object} 备份数据
     */
    backup() {
        const backupData = {};
        const keys = this.getAllKeys();

        keys.forEach(key => {
            backupData[key] = this.load(key);
        });

        const backupInfo = {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            totalKeys: keys.length,
            data: backupData
        };

        console.log(`💾 [存储] 数据备份完成: ${keys.length} 个键`);
        return backupInfo;
    }

    /**
     * 数据恢复
     * @SERVICE 数据恢复服务
     * @param {Object} backupInfo - 备份信息
     * @returns {Object} 恢复结果统计
     */
    restore(backupInfo) {
        if (!backupInfo || !backupInfo.data) {
            throw new Error('无效的备份数据');
        }

        const results = { success: 0, failed: 0, errors: [] };

        for (const [key, data] of Object.entries(backupInfo.data)) {
            if (this.save(key, data)) {
                results.success++;
            } else {
                results.failed++;
                results.errors.push(key);
            }
        }

        console.log(`🔄 [存储] 数据恢复完成: 成功 ${results.success}, 失败 ${results.failed}`);
        return results;
    }

    /**
     * 数据压缩存储
     * @SERVICE 压缩存储服务
     * @param {string} key - 存储键名
     * @param {any} data - 要保存的数据
     * @returns {boolean} 保存是否成功
     */
    saveCompressed(key, data) {
        try {
            const serializedData = JSON.stringify(data);
            // 简单的压缩：移除多余空白字符
            const compressedData = serializedData.replace(/\s+/g, ' ').trim();
            
            const compressionRatio = (1 - compressedData.length / serializedData.length) * 100;
            
            localStorage.setItem(this.prefix + key, compressedData);
            console.log(`🗜️ [存储] 压缩保存: ${key} (压缩率: ${compressionRatio.toFixed(1)}%)`);
            return true;
        } catch (error) {
            console.error(`❌ [存储] 压缩保存失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 获取存储统计信息
     * @SERVICE 存储统计服务
     * @returns {Object} 详细统计信息
     */
    getStats() {
        const info = this.getStorageInfo();
        const keys = this.getAllKeys();
        
        const stats = {
            ...info,
            keyDetails: keys.map(key => {
                const data = localStorage.getItem(this.prefix + key);
                return {
                    key,
                    size: data ? data.length : 0,
                    formattedSize: data ? getUtils()?.formatFileSize(data.length) || data.length + ' bytes' : '0 B'
                };
            }).sort((a, b) => b.size - a.size)
        };

        return stats;
    }
}

// ==================== 增强存储管理器类 ====================
/**
 * 增强存储管理器类 - 支持多层存储、压缩、加密等高级功能
 * @SERVICE 增强数据持久化管理器
 */
class EnhancedStorageManager extends StorageManager {
    constructor(eventBus = null) {
        super();
        this.eventBus = eventBus;
        this.compressionThreshold = 1024; // 1KB以上数据进行压缩
        this.encryptionEnabled = false;
        this.cache = new Map(); // 内存缓存
        this.accessLog = new Map(); // 访问日志
        this.performanceMetrics = {
            reads: 0,
            writes: 0,
            compressions: 0,
            decompressions: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };

        console.log('🚀 EnhancedStorageManager 初始化完成');
    }

    /**
     * 智能存储策略
     * @SERVICE 智能存储方法
     * @param {string} key - 存储键名
     * @param {any} data - 要保存的数据
     * @param {Object} options - 存储选项
     * @returns {boolean} 保存是否成功
     */
    async store(key, data, options = {}) {
        const size = this.getDataSize(data);
        const type = options.type || 'general';
        const priority = options.priority || 'normal';

        const storageItem = {
            key,
            data,
            size,
            type,
            priority,
            timestamp: Date.now(),
            compressed: false
        };

        try {
            // 检查是否需要压缩
            if (size > this.compressionThreshold) {
                storageItem.data = this.compressData(data);
                storageItem.compressed = true;
                this.performanceMetrics.compressions++;
            }

            // 保存到localStorage
            const success = this.save(key, storageItem);

            if (success) {
                // 更新缓存
                this.cache.set(key, storageItem);

                // 记录访问日志
                this.logAccess(key, 'write');

                // 更新性能指标
                this.performanceMetrics.writes++;

                // 触发事件
                if (this.eventBus) {
                    this.eventBus.emit('storage.write', { key, size, type });
                }
            }

            return success;
        } catch (error) {
            this.performanceMetrics.errors++;
            console.error(`增强存储失败: ${key}`, error);
            return false;
        }
    }

    /**
     * 智能检索数据
     * @SERVICE 智能检索方法
     * @param {string} key - 存储键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 检索的数据
     */
    async retrieve(key, defaultValue = null) {
        try {
            // 先检查缓存
            if (this.cache.has(key)) {
                this.performanceMetrics.cacheHits++;
                this.logAccess(key, 'cache_hit');

                const cachedItem = this.cache.get(key);
                return cachedItem.compressed ? this.decompressData(cachedItem.data) : cachedItem.data;
            }

            // 从localStorage加载
            const storageItem = this.load(key, null);
            if (storageItem === null) {
                this.performanceMetrics.cacheMisses++;
                return defaultValue;
            }

            // 解压缩数据
            let finalData = storageItem.data;
            if (storageItem.compressed) {
                finalData = this.decompressData(storageItem.data);
                this.performanceMetrics.decompressions++;
            }

            // 更新缓存
            this.cache.set(key, storageItem);

            // 记录访问日志
            this.logAccess(key, 'read');

            // 更新性能指标
            this.performanceMetrics.reads++;
            this.performanceMetrics.cacheMisses++;

            // 触发事件
            if (this.eventBus) {
                this.eventBus.emit('storage.read', { key, size: storageItem.size });
            }

            return finalData;
        } catch (error) {
            this.performanceMetrics.errors++;
            console.error(`增强检索失败: ${key}`, error);
            return defaultValue;
        }
    }

    /**
     * 简单数据压缩
     * @UTIL 数据压缩工具
     * @param {any} data - 要压缩的数据
     * @returns {string} 压缩后的数据
     */
    compressData(data) {
        try {
            const jsonString = JSON.stringify(data);
            // 简单压缩：移除多余空白和重复字符
            return jsonString.replace(/\s+/g, ' ').trim();
        } catch (error) {
            console.error('数据压缩失败:', error);
            return JSON.stringify(data);
        }
    }

    /**
     * 简单数据解压缩
     * @UTIL 数据解压缩工具
     * @param {string} compressedData - 压缩的数据
     * @returns {any} 解压缩后的数据
     */
    decompressData(compressedData) {
        try {
            return JSON.parse(compressedData);
        } catch (error) {
            console.error('数据解压缩失败:', error);
            return null;
        }
    }

    /**
     * 记录访问日志
     * @UTIL 访问日志记录工具
     * @param {string} key - 存储键名
     * @param {string} operation - 操作类型
     */
    logAccess(key, operation) {
        if (!this.accessLog.has(key)) {
            this.accessLog.set(key, { reads: 0, writes: 0, cacheHits: 0, lastAccess: null });
        }

        const log = this.accessLog.get(key);
        log.lastAccess = Date.now();

        switch (operation) {
            case 'read':
                log.reads++;
                break;
            case 'write':
                log.writes++;
                break;
            case 'cache_hit':
                log.cacheHits++;
                break;
        }
    }

    /**
     * 获取数据大小
     * @UTIL 数据大小计算工具
     * @param {any} data - 数据
     * @returns {number} 数据大小（字节）
     */
    getDataSize(data) {
        return JSON.stringify(data).length;
    }

    /**
     * 清理缓存
     * @SERVICE 缓存清理方法
     * @param {number} maxAge - 最大缓存时间（毫秒）
     */
    cleanupCache(maxAge = 3600000) { // 默认1小时
        const now = Date.now();
        const keysToRemove = [];

        for (const [key, item] of this.cache.entries()) {
            if (now - item.timestamp > maxAge) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => this.cache.delete(key));
        console.log(`🧹 缓存清理完成: 移除 ${keysToRemove.length} 个过期项`);
    }

    /**
     * 获取性能指标
     * @SERVICE 性能指标获取方法
     * @returns {Object} 性能指标
     */
    getPerformanceMetrics() {
        const cacheHitRate = this.performanceMetrics.cacheHits /
            (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100;

        return {
            ...this.performanceMetrics,
            cacheHitRate: isNaN(cacheHitRate) ? 0 : cacheHitRate.toFixed(2),
            cacheSize: this.cache.size,
            accessLogSize: this.accessLog.size
        };
    }

    /**
     * 获取访问统计
     * @SERVICE 访问统计获取方法
     * @returns {Array} 访问统计列表
     */
    getAccessStats() {
        return Array.from(this.accessLog.entries())
            .map(([key, log]) => ({ key, ...log }))
            .sort((a, b) => (b.reads + b.writes) - (a.reads + a.writes));
    }

    /**
     * 销毁增强存储管理器
     * @LIFECYCLE 资源清理方法
     */
    destroy() {
        this.cache.clear();
        this.accessLog.clear();
        console.log('🗑️ EnhancedStorageManager 已销毁');
    }
}

// ==================== 全局存储管理实例 ====================
let storageManagerInstance = null;

/**
 * 获取全局存储管理实例
 * @SERVICE 全局存储管理获取函数
 */
function getStorageManager() {
    if (!storageManagerInstance) {
        storageManagerInstance = new EnhancedStorageManager();
    }
    return storageManagerInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['storage-manager.js'] = {
    StorageManager,
    EnhancedStorageManager,
    getStorageManager
};
