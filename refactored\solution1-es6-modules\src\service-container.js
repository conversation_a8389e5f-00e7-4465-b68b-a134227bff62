/**
 * ==================== GoMyHire 对话分析系统 - 服务容器系统 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Service Container System
 * 版本: 2.0.0
 * 功能描述: 提供依赖注入容器，管理服务的注册、创建、生命周期和依赖关系
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: utils.js (safeExecute, createError, generateUniqueId)
 * 间接依赖: 无
 * 被依赖: main.js, ui.js, 以及需要依赖注入的高级模块
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 1 (第二层，基础设施模块)
 * 加载时机: core (核心模块，立即加载)
 * 加载条件: 依赖utils.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 服务容器管理 (ServiceContainer类)
 *   - 服务注册和工厂模式
 *   - 单例服务管理
 *   - 依赖关系解析
 *   - 标签中心管理 (TagCenter类)
 *   - 全局服务访问接口
 * 导出接口: ServiceContainer, TagCenter, getServiceContainer
 * 全局注册: window.ModuleExports['service-container.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 服务定义、依赖配置、服务请求
 * 输出数据: 服务实例、依赖解析结果
 * 状态管理: 维护服务实例映射、工厂函数、依赖关系
 * 事件处理: 通过EventBus集成事件系统
 * 
 * @INTEGRATION (集成关系)
 * UI集成: 为UI组件提供服务访问能力
 * 服务集成: 作为所有服务的管理中心
 * 存储集成: 可注册存储相关服务
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (维护服务实例缓存)
 * 加载性能: 快速 (懒加载机制)
 * 运行时性能: 良好 (缓存机制，避免重复创建)
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// ==================== 服务容器类 ====================
/**
 * 服务容器类 - 管理服务的注册、创建和生命周期
 * @SERVICE 服务容器管理器
 */
class ServiceContainer {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.services = new Map(); // 服务实例存储
        this.factories = new Map(); // 服务工厂函数
        this.singletons = new Map(); // 单例服务
        this.dependencies = new Map(); // 服务依赖关系
        this.isInitialized = true;

        console.log('🏗️ ServiceContainer 初始化完成');
    }

    /**
     * 注册服务
     * @SERVICE 服务注册方法
     * @param {string} name - 服务名称
     * @param {Function} factory - 服务工厂函数
     * @param {Object} options - 配置选项
     */
    register(name, factory, options = {}) {
        const config = {
            singleton: options.singleton || false,
            dependencies: options.dependencies || [],
            lazy: options.lazy || true,
            ...options
        };

        this.factories.set(name, { factory, config });
        this.dependencies.set(name, config.dependencies);

        // 如果不是懒加载，立即创建实例
        if (!config.lazy) {
            this.get(name);
        }

        console.log(`📦 服务已注册: ${name}`);
    }

    /**
     * 获取服务实例
     * @SERVICE 服务获取方法
     * @param {string} name - 服务名称
     * @returns {any} 服务实例
     */
    get(name) {
        // 检查单例缓存
        if (this.singletons.has(name)) {
            return this.singletons.get(name);
        }

        // 检查服务是否已注册
        if (!this.factories.has(name)) {
            throw new Error(`服务未注册: ${name}`);
        }

        const { factory, config } = this.factories.get(name);

        // 解析依赖
        const dependencies = this.resolveDependencies(config.dependencies);

        // 创建服务实例
        const instance = factory(...dependencies);

        // 如果是单例，缓存实例
        if (config.singleton) {
            this.singletons.set(name, instance);
        }

        return instance;
    }

    /**
     * 解析依赖
     * @SERVICE 依赖解析方法
     * @param {Array} dependencies - 依赖列表
     * @returns {Array} 解析后的依赖实例
     */
    resolveDependencies(dependencies) {
        return dependencies.map(dep => this.get(dep));
    }

    /**
     * 检查服务是否存在
     * @SERVICE 服务存在检查方法
     * @param {string} name - 服务名称
     * @returns {boolean} 是否存在
     */
    has(name) {
        return this.factories.has(name) || this.singletons.has(name);
    }

    /**
     * 移除服务
     * @SERVICE 服务移除方法
     * @param {string} name - 服务名称
     */
    remove(name) {
        this.factories.delete(name);
        this.singletons.delete(name);
        this.dependencies.delete(name);
        console.log(`🗑️ 服务已移除: ${name}`);
    }

    /**
     * 获取所有服务名称
     * @SERVICE 服务名称获取方法
     * @returns {Array} 服务名称列表
     */
    getServiceNames() {
        return Array.from(this.factories.keys());
    }

    /**
     * 销毁容器
     * @SERVICE 容器销毁方法
     */
    destroy() {
        this.services.clear();
        this.factories.clear();
        this.singletons.clear();
        this.dependencies.clear();
        console.log('🗑️ ServiceContainer 已销毁');
    }
}

// GlobalVariableRegistry 和 DependencyManager 已迁移到 dependency-manager.js
// 请从 dependency-manager.js 导入这些类

// DependencyManager 已迁移到 dependency-manager.js

// ==================== 标签中心管理器类 ====================
/**
 * 标签中心管理器类 - 统一管理系统中的标签和分类
 * @SERVICE 标签中心管理器
 */
class TagCenter {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.tags = new Map(); // 标签存储
        this.categories = new Map(); // 标签分类
        this.taggedItems = new Map(); // 被标记的项目
        this.isInitialized = true;

        // 预定义标签类别
        this.initializeTagCategories();

        console.log('🏷️ TagCenter 初始化完成');
    }

    /**
     * 初始化标签类别
     * @INIT 标签类别初始化方法
     */
    initializeTagCategories() {
        // 功能标签类别
        this.categories.set('FUNCTIONAL', {
            name: '功能标签',
            description: '标记具体功能实现',
            color: '#DDA0DD',
            tags: [
                '@SERVICE', '@MANAGER', '@FACTORY', '@UTIL', '@COMPONENT',
                '@HOOK', '@EVENT_HANDLER', '@TIMER', '@DATA_STRUCTURE',
                '@ENUMERATION', '@CONFIG_FILE', '@I18N_PACKAGE',
                '@ROUTE_MODULE', '@LAYOUT_COMPONENT', '@SHARED_STYLE',
                '@SHARED_COMPONENT', '@SHARED_UTIL', '@SHARED_VARIABLE',
                '@SHARED_CONSTANT'
            ]
        });

        // 生命周期标签类别
        this.categories.set('LIFECYCLE', {
            name: '生命周期标签',
            description: '标记组件生命周期相关代码',
            color: '#96CEB4',
            tags: ['@LIFECYCLE', '@INIT', '@DECLARATION']
        });

        console.log('✓ 标签类别初始化完成');
    }

    /**
     * 注册标签
     * @SERVICE 标签注册方法
     * @param {string} tag - 标签名称
     * @param {any} item - 被标记的项目
     * @param {Object} metadata - 元数据
     */
    registerTag(tag, item, metadata = {}) {
        if (!tag.startsWith('@')) {
            tag = '@' + tag;
        }

        const tagInfo = {
            tag, item,
            metadata: {
                type: metadata.type || 'unknown',
                description: metadata.description || '',
                location: metadata.location || '',
                module: metadata.module || 'unknown',
                createdAt: Date.now(),
                ...metadata
            }
        };

        if (!this.tags.has(tag)) {
            this.tags.set(tag, []);
        }

        this.tags.get(tag).push(tagInfo);

        // 建立反向映射
        const itemKey = this.generateItemKey(item);
        if (!this.taggedItems.has(itemKey)) {
            this.taggedItems.set(itemKey, []);
        }
        this.taggedItems.get(itemKey).push(tag);

        return this;
    }

    /**
     * 根据标签查找项目
     * @SERVICE 标签查找方法
     * @param {string} tag - 标签名称
     * @returns {Array} 标记的项目列表
     */
    findByTag(tag) {
        if (!tag.startsWith('@')) {
            tag = '@' + tag;
        }
        return this.tags.get(tag) || [];
    }

    /**
     * 获取标签统计
     * @SERVICE 标签统计获取方法
     * @returns {Object} 标签统计信息
     */
    getStats() {
        const stats = {
            totalTags: this.tags.size,
            totalItems: 0,
            totalCategories: this.categories.size,
            byCategory: {}
        };

        // 统计项目数量
        for (const items of this.tags.values()) {
            stats.totalItems += items.length;
        }

        // 按类别统计
        for (const [categoryName, categoryInfo] of this.categories) {
            stats.byCategory[categoryName] = {
                name: categoryInfo.name,
                tagCount: categoryInfo.tags.length,
                itemCount: 0
            };

            // 统计该类别下的项目数量
            for (const tag of categoryInfo.tags) {
                const items = this.tags.get(tag) || [];
                stats.byCategory[categoryName].itemCount += items.length;
            }
        }

        return stats;
    }

    /**
     * 生成项目键
     * @UTIL 项目键生成工具
     * @param {any} item - 项目
     * @returns {string} 项目键
     */
    generateItemKey(item) {
        if (typeof item === 'function') {
            return `func_${item.name || 'anonymous'}`;
        } else if (typeof item === 'object' && item !== null) {
            return `obj_${item.constructor.name}_${getUtils()?.generateUniqueId() || Date.now()}`;
        } else {
            return `val_${typeof item}_${String(item).slice(0, 20)}`;
        }
    }

    /**
     * 销毁标签中心
     * @SERVICE 标签中心销毁方法
     */
    destroy() {
        this.tags.clear();
        this.categories.clear();
        this.taggedItems.clear();
        console.log('🗑️ TagCenter 已销毁');
    }
}

// ==================== 全局服务容器实例 ====================
let serviceContainerInstance = null;

/**
 * 获取全局服务容器实例
 * @SERVICE 全局服务容器获取函数
 */
function getServiceContainer() {
    if (!serviceContainerInstance) {
        serviceContainerInstance = new ServiceContainer();
    }
    return serviceContainerInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['service-container.js'] = {
    ServiceContainer,
    TagCenter,
    getServiceContainer
};
