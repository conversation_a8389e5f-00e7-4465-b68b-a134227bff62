/**
 * ==================== GoMyHire 对话分析系统 - 依赖管理系统 ====================
 * @MANAGER 从standalone.html完整迁移的依赖管理系统
 * 实现全局变量注册、依赖关系管理、访问日志等功能
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// ==================== 全局变量注册中心类 ====================
/**
 * 全局变量注册中心类 - 统一管理全局变量和服务
 * @SHARED_COMPONENT 全局变量注册中心
 */
class GlobalVariableRegistry {
    constructor(eventBus = null) {
        this.variables = new Map(); // 变量存储
        this.metadata = new Map(); // 变量元数据
        this.dependencies = new Map(); // 依赖关系
        this.watchers = new Map(); // 变量监听器
        this.eventBus = eventBus;
        this.tags = new Map(); // 变量标签映射

        console.log('📋 GlobalVariableRegistry 初始化完成');
    }

    /**
     * 注册变量
     * @SERVICE 变量注册方法
     * @param {string} name - 变量名称
     * @param {any} value - 变量值
     * @param {Object} options - 配置选项
     */
    register(name, value, options = {}) {
        const {
            type = 'unknown',
            description = '',
            dependencies = [],
            tags = [],
            readonly = false,
            lazy = false,
            factory = null,
            validator = null,
            lifecycle = 'persistent'
        } = options;

        // 如果是工厂模式，延迟创建
        if (factory && lazy) {
            value = null;
        } else if (factory) {
            value = factory();
        }

        // 验证器检查
        if (validator && !validator(value)) {
            throw new Error(`变量验证失败: ${name}`);
        }

        // 存储变量和元数据
        this.variables.set(name, value);
        this.metadata.set(name, {
            type, description, dependencies, tags, readonly, lazy, factory, validator, lifecycle,
            createdAt: Date.now(),
            lastModified: Date.now(),
            accessCount: 0
        });

        // 建立依赖关系
        this.dependencies.set(name, dependencies);

        // 标签映射
        tags.forEach(tag => {
            if (!this.tags.has(tag)) {
                this.tags.set(tag, []);
            }
            this.tags.get(tag).push(name);
        });

        // 设置到全局对象
        if (value !== null) {
            window[name] = value;
        }

        // 触发注册事件
        if (this.eventBus) {
            this.eventBus.emit('variable.registered', { name, type, tags });
        }

        console.log(`📝 变量已注册: ${name} (${type})`);
        return this;
    }

    /**
     * 获取变量
     * @SERVICE 变量获取方法
     * @param {string} name - 变量名称
     * @returns {any} 变量值
     */
    get(name) {
        if (!this.variables.has(name)) {
            throw new Error(`变量未注册: ${name}`);
        }

        const metadata = this.metadata.get(name);
        let value = this.variables.get(name);

        // 懒加载处理
        if (value === null && metadata.lazy && metadata.factory) {
            value = metadata.factory();
            this.variables.set(name, value);
            window[name] = value;
            console.log(`🔄 懒加载变量: ${name}`);
        }

        // 更新访问统计
        metadata.accessCount++;
        return value;
    }

    /**
     * 设置变量值
     * @SERVICE 变量设置方法
     * @param {string} name - 变量名称
     * @param {any} value - 新值
     */
    set(name, value) {
        if (!this.variables.has(name)) {
            throw new Error(`变量未注册: ${name}`);
        }

        const metadata = this.metadata.get(name);
        if (metadata.readonly) {
            throw new Error(`变量为只读: ${name}`);
        }

        const oldValue = this.variables.get(name);
        this.variables.set(name, value);
        metadata.lastModified = Date.now();
        window[name] = value;

        // 触发监听器
        if (this.watchers.has(name)) {
            this.watchers.get(name).forEach(watcher => watcher(value, oldValue));
        }

        // 触发变量变更事件
        if (this.eventBus) {
            this.eventBus.emit('variable.changed', { name, oldValue, newValue: value });
        }

        console.log(`✓ 变量已更新: ${name}`);
    }

    /**
     * 添加变量监听器
     * @SERVICE 变量监听器添加方法
     * @param {string} name - 变量名称
     * @param {Function} watcher - 监听器函数
     * @returns {string} 监听器ID
     */
    watch(name, watcher) {
        if (!this.watchers.has(name)) {
            this.watchers.set(name, []);
        }

        const watcherId = getUtils()?.generateUniqueId('watcher') || 'watcher_' + Date.now();
        const watcherWrapper = { id: watcherId, watcher };
        this.watchers.get(name).push(watcherWrapper);

        return watcherId;
    }

    /**
     * 移除变量监听器
     * @SERVICE 变量监听器移除方法
     * @param {string} name - 变量名称
     * @param {string} watcherId - 监听器ID
     */
    unwatch(name, watcherId) {
        if (!this.watchers.has(name)) return;

        const watchers = this.watchers.get(name);
        const index = watchers.findIndex(w => w.id === watcherId);
        if (index !== -1) {
            watchers.splice(index, 1);
        }
    }

    /**
     * 根据标签获取变量
     * @SERVICE 标签查询方法
     * @param {string} tag - 标签名称
     * @returns {Array} 变量名称列表
     */
    getByTag(tag) {
        return this.tags.get(tag) || [];
    }

    /**
     * 获取统计信息
     * @SERVICE 统计信息获取方法
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            totalVariables: this.variables.size,
            totalTags: this.tags.size,
            byType: {},
            byModule: {}
        };

        for (const metadata of this.metadata.values()) {
            stats.byType[metadata.type] = (stats.byType[metadata.type] || 0) + 1;
        }

        return stats;
    }

    /**
     * 检查变量是否存在
     * @SERVICE 变量存在检查方法
     * @param {string} name - 变量名称
     * @returns {boolean} 是否存在
     */
    has(name) {
        return this.variables.has(name);
    }

    /**
     * 销毁注册中心
     * @SERVICE 注册中心销毁方法
     */
    destroy() {
        this.variables.clear();
        this.metadata.clear();
        this.dependencies.clear();
        this.watchers.clear();
        this.tags.clear();
        console.log('🗑️ GlobalVariableRegistry 已销毁');
    }
}

// ==================== 依赖管理器类 ====================
/**
 * 依赖管理器类 - 管理模块间的依赖关系
 * @MANAGER 依赖管理器
 */
class DependencyManager {
    constructor(globalRegistry = null, eventBus = null) {
        this.globalRegistry = globalRegistry;
        this.eventBus = eventBus;
        this.dependencies = new Map(); // 依赖映射
        this.proxies = new Map(); // 代理对象映射
        this.accessLog = new Map(); // 访问日志

        console.log('🔗 DependencyManager 初始化完成');
    }

    /**
     * 注册依赖
     * @SERVICE 依赖注册方法
     * @param {string} name - 依赖名称
     * @param {any} target - 依赖目标
     * @param {Object} options - 配置选项
     */
    registerDependency(name, target, options = {}) {
        const {
            type = 'direct',
            lazy = false,
            cached = true,
            validator = null
        } = options;

        const dependencyInfo = {
            name, target, type, lazy, cached, validator,
            createdAt: Date.now(),
            accessCount: 0
        };

        this.dependencies.set(name, dependencyInfo);

        // 如果需要，创建代理对象
        if (type === 'proxy') {
            this.createProxy(name, target);
        }

        console.log(`🔗 依赖已注册: ${name} (${type})`);
        return this;
    }

    /**
     * 获取依赖
     * @SERVICE 依赖获取方法
     * @param {string} name - 依赖名称
     * @returns {any} 依赖对象
     */
    getDependency(name) {
        const dependency = this.dependencies.get(name);
        if (!dependency) {
            // 尝试从全局注册中心获取
            if (this.globalRegistry && this.globalRegistry.has(name)) {
                return this.globalRegistry.get(name);
            }
            // 尝试从window对象获取
            if (window[name] !== undefined) {
                return window[name];
            }
            throw new Error(`依赖未注册: ${name}`);
        }

        // 记录访问
        dependency.accessCount++;
        this.logAccess(name);

        return dependency.target;
    }

    /**
     * 创建代理对象
     * @UTIL 代理对象创建工具
     * @param {string} name - 依赖名称
     * @param {any} target - 目标对象
     */
    createProxy(name, target) {
        const proxy = new Proxy(target, {
            get: (obj, prop) => {
                this.logAccess(name, prop, 'get');
                return obj[prop];
            },
            set: (obj, prop, value) => {
                this.logAccess(name, prop, 'set');
                obj[prop] = value;
                return true;
            }
        });

        this.proxies.set(name, proxy);
        return proxy;
    }

    /**
     * 记录访问日志
     * @UTIL 访问日志记录工具
     * @param {string} name - 依赖名称
     * @param {string} property - 属性名称
     * @param {string} operation - 操作类型
     */
    logAccess(name, property = null, operation = 'get') {
        const key = `${name}${property ? '.' + property : ''}`;
        if (!this.accessLog.has(key)) {
            this.accessLog.set(key, { count: 0, lastAccess: null });
        }

        const log = this.accessLog.get(key);
        log.count++;
        log.lastAccess = Date.now();
    }

    /**
     * 获取访问统计
     * @SERVICE 访问统计获取方法
     * @returns {Object} 访问统计信息
     */
    getAccessStats() {
        const stats = {
            totalDependencies: this.dependencies.size,
            totalAccesses: 0,
            mostAccessed: []
        };

        // 统计总访问次数
        for (const dependency of this.dependencies.values()) {
            stats.totalAccesses += dependency.accessCount;
        }

        // 找出最常访问的依赖
        const sortedDeps = Array.from(this.dependencies.entries())
            .sort(([,a], [,b]) => b.accessCount - a.accessCount)
            .slice(0, 10);

        stats.mostAccessed = sortedDeps.map(([name, dep]) => ({
            name,
            accessCount: dep.accessCount,
            type: dep.type
        }));

        return stats;
    }

    /**
     * 检查依赖是否存在
     * @SERVICE 依赖存在检查方法
     * @param {string} name - 依赖名称
     * @returns {boolean} 是否存在
     */
    has(name) {
        return this.dependencies.has(name) ||
               (this.globalRegistry && this.globalRegistry.has(name)) ||
               window[name] !== undefined;
    }

    /**
     * 移除依赖
     * @SERVICE 依赖移除方法
     * @param {string} name - 依赖名称
     */
    removeDependency(name) {
        this.dependencies.delete(name);
        this.proxies.delete(name);
        console.log(`🗑️ 依赖已移除: ${name}`);
    }

    /**
     * 清理依赖管理器
     * @SERVICE 依赖管理器清理方法
     */
    cleanup() {
        this.dependencies.clear();
        this.proxies.clear();
        this.accessLog.clear();
        console.log('🧹 DependencyManager 已清理');
    }
}

// ==================== 全局依赖管理实例 ====================
let dependencyManagerInstance = null;

/**
 * 获取全局依赖管理实例
 * @SERVICE 全局依赖管理获取函数
 */
function getDependencyManager() {
    if (!dependencyManagerInstance) {
        dependencyManagerInstance = new DependencyManager();
    }
    return dependencyManagerInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['dependency-manager.js'] = {
    GlobalVariableRegistry,
    DependencyManager,
    getDependencyManager
};
