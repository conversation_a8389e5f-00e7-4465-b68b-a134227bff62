/**
 * ==================== GoMyHire 对话分析系统 - 浏览器兼容性测试器 ====================
 * @SERVICE 浏览器兼容性测试工具
 * 实现跨浏览器兼容性测试，确保ES6模块系统在所有目标浏览器中正常工作
 */

import { generateUniqueId, safeExecute } from './utils.js';

// ==================== 浏览器兼容性测试器类 ====================
/**
 * 浏览器兼容性测试器类 - 负责跨浏览器兼容性测试
 * @COMPONENT 浏览器兼容性测试器
 */
export class BrowserCompatibilityTester {
    constructor() {
        this.testResults = new Map();
        this.isRunning = false;
        this.browserInfo = null;
        this.compatibilityMatrix = new Map();

        this.initialize();
        console.log('🌐 BrowserCompatibilityTester 初始化完成');
    }

    /**
     * 初始化兼容性测试器
     * @INIT 兼容性测试器初始化方法
     */
    initialize() {
        this.detectBrowserInfo();
        this.setupCompatibilityMatrix();
    }

    /**
     * 检测浏览器信息
     * @SERVICE 浏览器信息检测方法
     */
    detectBrowserInfo() {
        const userAgent = navigator.userAgent;
        const browserInfo = {
            userAgent: userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            vendor: navigator.vendor,
            appName: navigator.appName,
            appVersion: navigator.appVersion
        };

        // 检测浏览器类型和版本
        if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
            browserInfo.name = 'Chrome';
            const match = userAgent.match(/Chrome\/(\d+)/);
            browserInfo.version = match ? parseInt(match[1]) : 0;
        } else if (userAgent.includes('Firefox')) {
            browserInfo.name = 'Firefox';
            const match = userAgent.match(/Firefox\/(\d+)/);
            browserInfo.version = match ? parseInt(match[1]) : 0;
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            browserInfo.name = 'Safari';
            const match = userAgent.match(/Version\/(\d+)/);
            browserInfo.version = match ? parseInt(match[1]) : 0;
        } else if (userAgent.includes('Edg')) {
            browserInfo.name = 'Edge';
            const match = userAgent.match(/Edg\/(\d+)/);
            browserInfo.version = match ? parseInt(match[1]) : 0;
        } else {
            browserInfo.name = 'Unknown';
            browserInfo.version = 0;
        }

        this.browserInfo = browserInfo;
        console.log(`🌐 检测到浏览器: ${browserInfo.name} ${browserInfo.version}`);
    }

    /**
     * 设置兼容性测试矩阵
     * @SERVICE 兼容性测试矩阵设置方法
     */
    setupCompatibilityMatrix() {
        // ES6模块支持测试
        this.compatibilityMatrix.set('es6Modules', {
            name: 'ES6模块支持',
            description: '测试ES6 import/export语法支持',
            minVersions: {
                Chrome: 61,
                Firefox: 60,
                Safari: 11,
                Edge: 16
            },
            testMethod: 'testES6ModuleSupport'
        });

        // 现代JavaScript特性测试
        this.compatibilityMatrix.set('modernJS', {
            name: '现代JavaScript特性',
            description: '测试async/await、箭头函数、解构等特性',
            minVersions: {
                Chrome: 55,
                Firefox: 52,
                Safari: 10,
                Edge: 14
            },
            testMethod: 'testModernJSFeatures'
        });

        // Web APIs测试
        this.compatibilityMatrix.set('webAPIs', {
            name: 'Web APIs支持',
            description: '测试localStorage、fetch、FileReader等API',
            minVersions: {
                Chrome: 40,
                Firefox: 40,
                Safari: 10,
                Edge: 12
            },
            testMethod: 'testWebAPIs'
        });

        // CSS特性测试
        this.compatibilityMatrix.set('cssFeatures', {
            name: 'CSS特性支持',
            description: '测试Grid、Flexbox、CSS变量等特性',
            minVersions: {
                Chrome: 57,
                Firefox: 52,
                Safari: 10,
                Edge: 16
            },
            testMethod: 'testCSSFeatures'
        });

        // 文件处理API测试
        this.compatibilityMatrix.set('fileAPIs', {
            name: '文件处理API',
            description: '测试File API、拖拽API等文件处理功能',
            minVersions: {
                Chrome: 13,
                Firefox: 3.6,
                Safari: 6,
                Edge: 12
            },
            testMethod: 'testFileAPIs'
        });

        console.log(`📊 设置了 ${this.compatibilityMatrix.size} 个兼容性测试项`);
    }

    /**
     * 运行完整兼容性测试
     * @SERVICE 完整兼容性测试运行方法
     * @returns {Promise<Object>} 兼容性测试结果
     */
    async runFullCompatibilityTest() {
        if (this.isRunning) {
            console.warn('兼容性测试正在运行中');
            return null;
        }

        this.isRunning = true;
        const startTime = performance.now();

        try {
            console.log('🚀 开始浏览器兼容性测试...');

            const results = {
                browserInfo: this.browserInfo,
                summary: {
                    totalTests: 0,
                    passedTests: 0,
                    failedTests: 0,
                    warningTests: 0,
                    overallCompatibility: 0
                },
                testResults: {},
                recommendations: [],
                supportStatus: 'unknown'
            };

            // 运行所有兼容性测试
            for (const [testName, testConfig] of this.compatibilityMatrix) {
                console.log(`🧪 运行测试: ${testConfig.name}`);
                
                const testResult = await this.runCompatibilityTest(testName, testConfig);
                results.testResults[testName] = testResult;
                
                // 更新统计
                results.summary.totalTests++;
                if (testResult.status === 'passed') {
                    results.summary.passedTests++;
                } else if (testResult.status === 'warning') {
                    results.summary.warningTests++;
                } else {
                    results.summary.failedTests++;
                }
            }

            // 计算总体兼容性
            results.summary.overallCompatibility = this.calculateOverallCompatibility(results.summary);
            
            // 确定支持状态
            results.supportStatus = this.determineSupportStatus(results);
            
            // 生成建议
            results.recommendations = this.generateCompatibilityRecommendations(results);

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            console.log(`✅ 兼容性测试完成，总体兼容性: ${results.summary.overallCompatibility}%`);
            
            // 保存测试结果
            this.testResults.set(Date.now(), {
                ...results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            });

            return {
                success: true,
                results: results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ 兼容性测试失败:', error);
            return {
                success: false,
                error: error.message,
                browserInfo: this.browserInfo,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 运行单个兼容性测试
     * @SERVICE 单个兼容性测试运行方法
     * @param {string} testName - 测试名称
     * @param {Object} testConfig - 测试配置
     * @returns {Promise<Object>} 测试结果
     */
    async runCompatibilityTest(testName, testConfig) {
        const startTime = performance.now();
        
        try {
            // 检查浏览器版本要求
            const minVersion = testConfig.minVersions[this.browserInfo.name];
            const versionCheck = {
                required: minVersion || 0,
                current: this.browserInfo.version,
                supported: !minVersion || this.browserInfo.version >= minVersion
            };

            // 运行具体测试
            const testMethod = this[testConfig.testMethod];
            if (typeof testMethod !== 'function') {
                throw new Error(`测试方法不存在: ${testConfig.testMethod}`);
            }

            const testResult = await testMethod.call(this);
            const endTime = performance.now();

            // 确定测试状态
            let status = 'passed';
            if (!versionCheck.supported) {
                status = 'failed';
            } else if (testResult.warnings && testResult.warnings.length > 0) {
                status = 'warning';
            } else if (!testResult.passed) {
                status = 'failed';
            }

            return {
                testName: testName,
                status: status,
                versionCheck: versionCheck,
                testResult: testResult,
                duration: endTime - startTime
            };

        } catch (error) {
            const endTime = performance.now();
            
            return {
                testName: testName,
                status: 'failed',
                error: error.message,
                duration: endTime - startTime
            };
        }
    }

    // ==================== 具体兼容性测试方法 ====================

    /**
     * 测试ES6模块支持
     * @TEST ES6模块支持测试
     */
    async testES6ModuleSupport() {
        const results = {
            passed: true,
            warnings: [],
            details: {}
        };

        try {
            // 检查import/export语法支持
            results.details.importExportSupport = typeof import === 'function';
            
            // 检查动态import支持
            results.details.dynamicImportSupport = typeof import === 'function';
            
            // 检查模块加载器是否工作
            results.details.moduleLoaderWorking = !!(window.ModuleExports);
            
            // 检查已加载的模块数量
            const moduleCount = window.ModuleExports ? Object.keys(window.ModuleExports).length : 0;
            results.details.loadedModulesCount = moduleCount;
            results.details.hasLoadedModules = moduleCount > 0;

            if (!results.details.importExportSupport) {
                results.passed = false;
            }

            if (!results.details.moduleLoaderWorking) {
                results.warnings.push('模块加载器未正常工作');
            }

            if (moduleCount === 0) {
                results.warnings.push('没有检测到已加载的模块');
            }

        } catch (error) {
            results.passed = false;
            results.error = error.message;
        }

        return results;
    }

    /**
     * 测试现代JavaScript特性
     * @TEST 现代JavaScript特性测试
     */
    async testModernJSFeatures() {
        const results = {
            passed: true,
            warnings: [],
            details: {}
        };

        try {
            // 测试async/await
            results.details.asyncAwaitSupport = (async () => {
                try {
                    await Promise.resolve();
                    return true;
                } catch {
                    return false;
                }
            })();

            // 测试箭头函数
            results.details.arrowFunctionSupport = (() => {
                try {
                    const test = () => true;
                    return test();
                } catch {
                    return false;
                }
            })();

            // 测试解构赋值
            results.details.destructuringSupport = (() => {
                try {
                    const [a, b] = [1, 2];
                    const {x, y} = {x: 1, y: 2};
                    return a === 1 && b === 2 && x === 1 && y === 2;
                } catch {
                    return false;
                }
            })();

            // 测试模板字符串
            results.details.templateLiteralSupport = (() => {
                try {
                    const test = 'world';
                    return `hello ${test}` === 'hello world';
                } catch {
                    return false;
                }
            })();

            // 测试类语法
            results.details.classSupport = (() => {
                try {
                    class TestClass {
                        constructor() {
                            this.value = 'test';
                        }
                    }
                    const instance = new TestClass();
                    return instance.value === 'test';
                } catch {
                    return false;
                }
            })();

            // 检查所有特性
            const features = Object.values(results.details);
            const supportedFeatures = features.filter(Boolean).length;
            const totalFeatures = features.length;

            if (supportedFeatures < totalFeatures) {
                results.warnings.push(`${totalFeatures - supportedFeatures} 个现代JS特性不支持`);
            }

            if (supportedFeatures < totalFeatures * 0.8) {
                results.passed = false;
            }

        } catch (error) {
            results.passed = false;
            results.error = error.message;
        }

        return results;
    }

    /**
     * 测试Web APIs支持
     * @TEST Web APIs支持测试
     */
    async testWebAPIs() {
        const results = {
            passed: true,
            warnings: [],
            details: {}
        };

        try {
            // 测试localStorage
            results.details.localStorageSupport = (() => {
                try {
                    localStorage.setItem('test', 'value');
                    const value = localStorage.getItem('test');
                    localStorage.removeItem('test');
                    return value === 'value';
                } catch {
                    return false;
                }
            })();

            // 测试fetch API
            results.details.fetchSupport = typeof fetch === 'function';

            // 测试FileReader API
            results.details.fileReaderSupport = typeof FileReader === 'function';

            // 测试Promise
            results.details.promiseSupport = typeof Promise === 'function';

            // 测试JSON
            results.details.jsonSupport = typeof JSON === 'object' && 
                                         typeof JSON.parse === 'function' && 
                                         typeof JSON.stringify === 'function';

            // 测试console
            results.details.consoleSupport = typeof console === 'object' && 
                                            typeof console.log === 'function';

            // 检查关键API
            const criticalAPIs = [
                'localStorageSupport',
                'fileReaderSupport',
                'promiseSupport',
                'jsonSupport'
            ];

            const unsupportedAPIs = criticalAPIs.filter(api => !results.details[api]);
            
            if (unsupportedAPIs.length > 0) {
                results.warnings.push(`不支持的关键API: ${unsupportedAPIs.join(', ')}`);
                if (unsupportedAPIs.length > 1) {
                    results.passed = false;
                }
            }

        } catch (error) {
            results.passed = false;
            results.error = error.message;
        }

        return results;
    }

    /**
     * 测试CSS特性支持
     * @TEST CSS特性支持测试
     */
    async testCSSFeatures() {
        const results = {
            passed: true,
            warnings: [],
            details: {}
        };

        try {
            // 测试CSS Grid
            results.details.gridSupport = CSS.supports('display', 'grid');

            // 测试Flexbox
            results.details.flexboxSupport = CSS.supports('display', 'flex');

            // 测试CSS变量
            results.details.cssVariablesSupport = CSS.supports('color', 'var(--test)');

            // 测试CSS转换
            results.details.transformSupport = CSS.supports('transform', 'translateX(10px)');

            // 测试CSS过渡
            results.details.transitionSupport = CSS.supports('transition', 'all 0.3s ease');

            // 检查关键CSS特性
            const criticalFeatures = [
                'flexboxSupport',
                'transformSupport',
                'transitionSupport'
            ];

            const unsupportedFeatures = criticalFeatures.filter(feature => !results.details[feature]);
            
            if (unsupportedFeatures.length > 0) {
                results.warnings.push(`不支持的CSS特性: ${unsupportedFeatures.join(', ')}`);
            }

            if (!results.details.flexboxSupport) {
                results.passed = false;
            }

        } catch (error) {
            // CSS.supports可能不存在
            results.warnings.push('无法检测CSS特性支持');
            results.details.cssSupportsAvailable = false;
        }

        return results;
    }

    /**
     * 测试文件处理API
     * @TEST 文件处理API测试
     */
    async testFileAPIs() {
        const results = {
            passed: true,
            warnings: [],
            details: {}
        };

        try {
            // 测试File API
            results.details.fileAPISupport = typeof File === 'function';

            // 测试FileReader API
            results.details.fileReaderSupport = typeof FileReader === 'function';

            // 测试Blob API
            results.details.blobSupport = typeof Blob === 'function';

            // 测试拖拽API
            results.details.dragDropSupport = 'draggable' in document.createElement('div');

            // 测试FormData API
            results.details.formDataSupport = typeof FormData === 'function';

            // 检查文件处理能力
            const fileAPIs = [
                'fileAPISupport',
                'fileReaderSupport',
                'blobSupport'
            ];

            const unsupportedAPIs = fileAPIs.filter(api => !results.details[api]);
            
            if (unsupportedAPIs.length > 0) {
                results.warnings.push(`不支持的文件API: ${unsupportedAPIs.join(', ')}`);
                results.passed = false;
            }

            if (!results.details.dragDropSupport) {
                results.warnings.push('拖拽功能可能不完全支持');
            }

        } catch (error) {
            results.passed = false;
            results.error = error.message;
        }

        return results;
    }

    /**
     * 计算总体兼容性
     * @UTIL 总体兼容性计算工具
     * @param {Object} summary - 测试摘要
     * @returns {number} 兼容性百分比
     */
    calculateOverallCompatibility(summary) {
        if (summary.totalTests === 0) return 0;
        
        const passedWeight = 1.0;
        const warningWeight = 0.7;
        const failedWeight = 0.0;
        
        const weightedScore = (summary.passedTests * passedWeight + 
                              summary.warningTests * warningWeight + 
                              summary.failedTests * failedWeight) / summary.totalTests;
        
        return Math.round(weightedScore * 100);
    }

    /**
     * 确定支持状态
     * @UTIL 支持状态确定工具
     * @param {Object} results - 测试结果
     * @returns {string} 支持状态
     */
    determineSupportStatus(results) {
        const compatibility = results.summary.overallCompatibility;
        
        if (compatibility >= 90) {
            return 'fully-supported';
        } else if (compatibility >= 75) {
            return 'mostly-supported';
        } else if (compatibility >= 50) {
            return 'partially-supported';
        } else {
            return 'not-supported';
        }
    }

    /**
     * 生成兼容性建议
     * @UTIL 兼容性建议生成工具
     * @param {Object} results - 测试结果
     * @returns {Array} 建议列表
     */
    generateCompatibilityRecommendations(results) {
        const recommendations = [];
        const browser = this.browserInfo;
        
        if (results.supportStatus === 'not-supported') {
            recommendations.push(`当前浏览器 ${browser.name} ${browser.version} 不支持此应用，建议升级浏览器`);
        } else if (results.supportStatus === 'partially-supported') {
            recommendations.push(`当前浏览器支持有限，某些功能可能无法正常工作`);
        }

        // 检查ES6模块支持
        const es6Test = results.testResults.es6Modules;
        if (es6Test && es6Test.status === 'failed') {
            recommendations.push('浏览器不支持ES6模块，请升级到支持ES6模块的版本');
        }

        // 检查文件API支持
        const fileTest = results.testResults.fileAPIs;
        if (fileTest && fileTest.status === 'failed') {
            recommendations.push('文件处理功能可能无法正常工作，建议升级浏览器');
        }

        // 浏览器特定建议
        if (browser.name === 'Chrome' && browser.version < 61) {
            recommendations.push('建议升级Chrome到61+版本以获得完整ES6模块支持');
        } else if (browser.name === 'Firefox' && browser.version < 60) {
            recommendations.push('建议升级Firefox到60+版本以获得完整ES6模块支持');
        } else if (browser.name === 'Safari' && browser.version < 11) {
            recommendations.push('建议升级Safari到11+版本以获得完整ES6模块支持');
        } else if (browser.name === 'Edge' && browser.version < 16) {
            recommendations.push('建议升级Edge到16+版本以获得完整ES6模块支持');
        }

        if (recommendations.length === 0) {
            recommendations.push('浏览器兼容性良好，应用应该能正常运行');
        }

        return recommendations;
    }

    /**
     * 获取兼容性测试历史
     * @SERVICE 兼容性测试历史获取方法
     * @returns {Array} 历史测试结果
     */
    getCompatibilityHistory() {
        return Array.from(this.testResults.entries()).map(([timestamp, results]) => ({
            timestamp: new Date(timestamp).toISOString(),
            results: results
        }));
    }

    /**
     * 清除兼容性测试历史
     * @SERVICE 兼容性测试历史清除方法
     */
    clearCompatibilityHistory() {
        this.testResults.clear();
        console.log('🧹 兼容性测试历史已清除');
    }

    /**
     * 销毁兼容性测试器
     * @LIFECYCLE 兼容性测试器销毁方法
     */
    destroy() {
        this.clearCompatibilityHistory();
        this.compatibilityMatrix.clear();
        this.browserInfo = null;
        console.log('🗑️ BrowserCompatibilityTester 已销毁');
    }
}

// ==================== 全局实例 ====================
let globalBrowserCompatibilityTester = null;

/**
 * 获取全局浏览器兼容性测试器实例
 * @SERVICE 全局浏览器兼容性测试器获取函数
 * @returns {BrowserCompatibilityTester} 浏览器兼容性测试器实例
 */
export function getBrowserCompatibilityTester() {
    if (!globalBrowserCompatibilityTester) {
        globalBrowserCompatibilityTester = new BrowserCompatibilityTester();
    }
    return globalBrowserCompatibilityTester;
}

// ==================== 便捷函数 ====================

/**
 * 运行浏览器兼容性测试
 * @SERVICE 浏览器兼容性测试运行函数
 * @returns {Promise<Object>} 兼容性测试结果
 */
export async function runBrowserCompatibilityTest() {
    return await getBrowserCompatibilityTester().runFullCompatibilityTest();
}

/**
 * 快速浏览器检查
 * @SERVICE 快速浏览器检查函数
 * @returns {Object} 浏览器检查结果
 */
export function quickBrowserCheck() {
    const tester = getBrowserCompatibilityTester();
    
    return {
        browserInfo: tester.browserInfo,
        basicSupport: {
            es6Modules: typeof import === 'function',
            localStorage: typeof localStorage === 'object',
            fileReader: typeof FileReader === 'function',
            fetch: typeof fetch === 'function'
        },
        timestamp: Date.now()
    };
}
