/**
 * ==================== GoMyHire 对话分析系统 - 功能对比测试器 ====================
 * @SERVICE 新系统与原standalone.html的功能对比测试工具
 * 实现全面的功能对比测试，确保所有功能都已正确迁移
 */

import { generateUniqueId, safeExecute } from './utils.js';
import { getFunctionalTester } from './functional-tester.js';
import { getPerformanceTester } from './performance-tester.js';

// ==================== 功能对比测试器类 ====================
/**
 * 功能对比测试器类 - 负责新旧系统功能对比测试
 * @COMPONENT 功能对比测试器
 */
export class ComparisonTester {
    constructor() {
        this.functionalTester = null;
        this.performanceTester = null;
        this.comparisonResults = new Map();
        this.isRunning = false;
        this.featureMatrix = new Map();

        this.initialize();
        console.log('🔍 ComparisonTester 初始化完成');
    }

    /**
     * 初始化对比测试器
     * @INIT 对比测试器初始化方法
     */
    initialize() {
        this.functionalTester = getFunctionalTester();
        this.performanceTester = getPerformanceTester();
        this.setupFeatureMatrix();
    }

    /**
     * 设置功能对比矩阵
     * @SERVICE 功能对比矩阵设置方法
     */
    setupFeatureMatrix() {
        // 核心功能对比
        this.featureMatrix.set('coreFeatures', {
            name: '核心功能对比',
            features: [
                {
                    name: '文件上传',
                    original: 'drag-drop zone + file input',
                    new: 'enhanced-upload.js + drag-upload.js',
                    testMethod: 'testFileUpload'
                },
                {
                    name: '文本解析',
                    original: 'inline parsing functions',
                    new: 'parser.js module',
                    testMethod: 'testTextParsing'
                },
                {
                    name: '数据存储',
                    original: 'localStorage direct access',
                    new: 'storage-manager.js + storage.js',
                    testMethod: 'testDataStorage'
                },
                {
                    name: 'AI分析',
                    original: 'inline Kimi API calls',
                    new: 'parser.js with AI integration',
                    testMethod: 'testAIAnalysis'
                },
                {
                    name: '图表显示',
                    original: 'inline ECharts code',
                    new: 'charts.js module',
                    testMethod: 'testChartDisplay'
                }
            ]
        });

        // UI组件对比
        this.featureMatrix.set('uiComponents', {
            name: 'UI组件对比',
            features: [
                {
                    name: '通知系统',
                    original: 'inline notification functions',
                    new: 'notification.js module',
                    testMethod: 'testNotificationSystem'
                },
                {
                    name: '模态框',
                    original: 'inline modal functions',
                    new: 'modal.js module',
                    testMethod: 'testModalSystem'
                },
                {
                    name: '标签页',
                    original: 'inline tab switching',
                    new: 'tabs.js module',
                    testMethod: 'testTabSystem'
                },
                {
                    name: '进度显示',
                    original: 'inline progress bars',
                    new: 'progress.js module',
                    testMethod: 'testProgressSystem'
                }
            ]
        });

        // 高级功能对比
        this.featureMatrix.set('advancedFeatures', {
            name: '高级功能对比',
            features: [
                {
                    name: 'QA优化',
                    original: 'inline optimization logic',
                    new: 'qa-optimization.js module',
                    testMethod: 'testQAOptimization'
                },
                {
                    name: '报告生成',
                    original: 'inline report generation',
                    new: 'report-generator.js module',
                    testMethod: 'testReportGeneration'
                },
                {
                    name: '标签管理',
                    original: 'inline tag management',
                    new: 'tag-center.js module',
                    testMethod: 'testTagManagement'
                }
            ]
        });

        // 系统架构对比
        this.featureMatrix.set('systemArchitecture', {
            name: '系统架构对比',
            features: [
                {
                    name: '模块化',
                    original: 'monolithic 31K+ lines',
                    new: 'modular ES6 modules',
                    testMethod: 'testModularization'
                },
                {
                    name: '依赖管理',
                    original: 'global variables',
                    new: 'dependency-manager.js',
                    testMethod: 'testDependencyManagement'
                },
                {
                    name: '事件系统',
                    original: 'direct DOM events',
                    new: 'event-bus.js',
                    testMethod: 'testEventSystem'
                },
                {
                    name: '服务容器',
                    original: 'no service management',
                    new: 'service-container.js',
                    testMethod: 'testServiceContainer'
                }
            ]
        });

        console.log(`📊 设置了 ${this.featureMatrix.size} 个功能对比类别`);
    }

    /**
     * 运行完整功能对比测试
     * @SERVICE 完整功能对比测试运行方法
     * @returns {Promise<Object>} 对比测试结果
     */
    async runFullComparisonTest() {
        if (this.isRunning) {
            console.warn('功能对比测试正在运行中');
            return null;
        }

        this.isRunning = true;
        const startTime = performance.now();

        try {
            console.log('🚀 开始完整功能对比测试...');

            const results = {
                summary: {
                    totalFeatures: 0,
                    passedFeatures: 0,
                    failedFeatures: 0,
                    partialFeatures: 0,
                    overallScore: 0
                },
                categories: {},
                recommendations: [],
                migrationStatus: {}
            };

            // 运行所有功能类别的对比测试
            for (const [categoryName, category] of this.featureMatrix) {
                console.log(`📦 测试功能类别: ${category.name}`);
                
                const categoryResult = await this.runCategoryComparison(categoryName);
                results.categories[categoryName] = categoryResult;
                
                // 更新总体统计
                results.summary.totalFeatures += categoryResult.totalFeatures;
                results.summary.passedFeatures += categoryResult.passedFeatures;
                results.summary.failedFeatures += categoryResult.failedFeatures;
                results.summary.partialFeatures += categoryResult.partialFeatures;
            }

            // 计算总体评分
            results.summary.overallScore = this.calculateOverallScore(results.summary);
            
            // 生成建议
            results.recommendations = this.generateRecommendations(results);
            
            // 生成迁移状态报告
            results.migrationStatus = this.generateMigrationStatus(results);

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            console.log(`✅ 功能对比测试完成，总体评分: ${results.summary.overallScore}%`);
            
            // 保存测试结果
            this.comparisonResults.set(Date.now(), {
                ...results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            });

            return {
                success: true,
                results: results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ 功能对比测试失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 运行功能类别对比
     * @SERVICE 功能类别对比运行方法
     * @param {string} categoryName - 功能类别名称
     * @returns {Promise<Object>} 类别对比结果
     */
    async runCategoryComparison(categoryName) {
        const category = this.featureMatrix.get(categoryName);
        if (!category) {
            throw new Error(`功能类别不存在: ${categoryName}`);
        }

        const results = [];
        let passedFeatures = 0;
        let failedFeatures = 0;
        let partialFeatures = 0;

        for (const feature of category.features) {
            try {
                console.log(`  🧪 测试功能: ${feature.name}`);
                
                const featureResult = await this.runFeatureComparison(feature);
                results.push(featureResult);
                
                switch (featureResult.status) {
                    case 'passed':
                        passedFeatures++;
                        console.log(`    ✅ ${feature.name} - 完全兼容`);
                        break;
                    case 'partial':
                        partialFeatures++;
                        console.log(`    ⚠️ ${feature.name} - 部分兼容`);
                        break;
                    case 'failed':
                        failedFeatures++;
                        console.log(`    ❌ ${feature.name} - 不兼容`);
                        break;
                }
                
            } catch (error) {
                failedFeatures++;
                results.push({
                    featureName: feature.name,
                    status: 'failed',
                    error: error.message,
                    score: 0
                });
                console.log(`    ❌ ${feature.name} - 测试异常: ${error.message}`);
            }
        }

        return {
            categoryName: categoryName,
            totalFeatures: category.features.length,
            passedFeatures: passedFeatures,
            failedFeatures: failedFeatures,
            partialFeatures: partialFeatures,
            results: results
        };
    }

    /**
     * 运行单个功能对比
     * @SERVICE 单个功能对比运行方法
     * @param {Object} feature - 功能配置
     * @returns {Promise<Object>} 功能对比结果
     */
    async runFeatureComparison(feature) {
        const startTime = performance.now();
        
        try {
            // 根据测试方法调用对应的测试函数
            const testMethod = this[feature.testMethod];
            if (typeof testMethod !== 'function') {
                throw new Error(`测试方法不存在: ${feature.testMethod}`);
            }

            const result = await testMethod.call(this, feature);
            const endTime = performance.now();

            return {
                featureName: feature.name,
                status: result.status || 'passed',
                score: result.score || 100,
                details: result.details || {},
                originalImplementation: feature.original,
                newImplementation: feature.new,
                duration: endTime - startTime
            };

        } catch (error) {
            const endTime = performance.now();
            
            return {
                featureName: feature.name,
                status: 'failed',
                score: 0,
                error: error.message,
                originalImplementation: feature.original,
                newImplementation: feature.new,
                duration: endTime - startTime
            };
        }
    }

    // ==================== 具体功能测试方法 ====================

    /**
     * 测试文件上传功能
     * @TEST 文件上传功能测试
     */
    async testFileUpload(feature) {
        const details = {};
        
        // 检查拖拽区域
        const dropZone = document.getElementById('drop-zone');
        details.dropZoneExists = !!dropZone;
        
        // 检查文件输入
        const fileInput = document.getElementById('file-input');
        details.fileInputExists = !!fileInput;
        
        // 检查增强上传管理器
        const mainApp = window.mainApp;
        details.enhancedUploadAvailable = !!(mainApp && mainApp.uploadManager);
        
        const score = (details.dropZoneExists ? 30 : 0) + 
                     (details.fileInputExists ? 30 : 0) + 
                     (details.enhancedUploadAvailable ? 40 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试文本解析功能
     * @TEST 文本解析功能测试
     */
    async testTextParsing(feature) {
        const details = {};
        
        // 检查解析器模块
        const parserModule = window.ModuleExports && window.ModuleExports['parser.js'];
        details.parserModuleLoaded = !!parserModule;
        
        // 检查解析方法
        details.parseTxtContentExists = !!(parserModule && typeof parserModule.parseTxtContent === 'function');
        details.evaluateConversationExists = !!(parserModule && typeof parserModule.evaluateConversationWithKimi === 'function');
        
        const score = (details.parserModuleLoaded ? 40 : 0) + 
                     (details.parseTxtContentExists ? 30 : 0) + 
                     (details.evaluateConversationExists ? 30 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试数据存储功能
     * @TEST 数据存储功能测试
     */
    async testDataStorage(feature) {
        const details = {};
        
        // 检查存储模块
        const storageModule = window.ModuleExports && window.ModuleExports['storage.js'];
        details.storageModuleLoaded = !!storageModule;
        
        // 检查存储管理器
        const mainApp = window.mainApp;
        details.storageManagerAvailable = !!(mainApp && mainApp.storageManager);
        
        // 检查localStorage可用性
        try {
            localStorage.setItem('test', 'value');
            localStorage.removeItem('test');
            details.localStorageWorking = true;
        } catch (error) {
            details.localStorageWorking = false;
        }
        
        const score = (details.storageModuleLoaded ? 40 : 0) + 
                     (details.storageManagerAvailable ? 30 : 0) + 
                     (details.localStorageWorking ? 30 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试AI分析功能
     * @TEST AI分析功能测试
     */
    async testAIAnalysis(feature) {
        const details = {};
        
        // 检查解析器中的AI功能
        const parserModule = window.ModuleExports && window.ModuleExports['parser.js'];
        details.aiAnalysisMethodExists = !!(parserModule && typeof parserModule.evaluateConversationWithKimi === 'function');
        
        // 检查API配置
        const hasApiKey = !!(window.LOCAL_CONFIG && window.LOCAL_CONFIG.apiKey && 
                           window.LOCAL_CONFIG.apiKey !== 'sk-your-kimi-api-key-here');
        details.apiKeyConfigured = hasApiKey;
        
        const score = (details.aiAnalysisMethodExists ? 70 : 0) + 
                     (details.apiKeyConfigured ? 30 : 0);
        
        return {
            status: score >= 70 ? 'passed' : score >= 40 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试图表显示功能
     * @TEST 图表显示功能测试
     */
    async testChartDisplay(feature) {
        const details = {};
        
        // 检查图表模块
        const chartsModule = window.ModuleExports && window.ModuleExports['charts.js'];
        details.chartsModuleLoaded = !!chartsModule;
        
        // 检查ECharts库
        details.echartsAvailable = !!(window.echarts);
        
        // 检查图表容器
        const chartContainers = document.querySelectorAll('[id*="chart"]');
        details.chartContainersCount = chartContainers.length;
        
        const score = (details.chartsModuleLoaded ? 40 : 0) + 
                     (details.echartsAvailable ? 40 : 0) + 
                     (details.chartContainersCount > 0 ? 20 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试通知系统
     * @TEST 通知系统测试
     */
    async testNotificationSystem(feature) {
        const details = {};
        
        // 检查通知模块
        const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
        details.notificationModuleLoaded = !!notificationModule;
        
        // 检查通知管理器
        const mainApp = window.mainApp;
        details.notificationManagerAvailable = !!(mainApp && mainApp.notificationManager);
        
        const score = (details.notificationModuleLoaded ? 50 : 0) + 
                     (details.notificationManagerAvailable ? 50 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试模态框系统
     * @TEST 模态框系统测试
     */
    async testModalSystem(feature) {
        const details = {};
        
        // 检查模态框模块
        const modalModule = window.ModuleExports && window.ModuleExports['modal.js'];
        details.modalModuleLoaded = !!modalModule;
        
        // 检查模态框管理器
        const mainApp = window.mainApp;
        details.modalManagerAvailable = !!(mainApp && mainApp.modalManager);
        
        const score = (details.modalModuleLoaded ? 50 : 0) + 
                     (details.modalManagerAvailable ? 50 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试标签页系统
     * @TEST 标签页系统测试
     */
    async testTabSystem(feature) {
        const details = {};
        
        // 检查标签页模块
        const tabsModule = window.ModuleExports && window.ModuleExports['tabs.js'];
        details.tabsModuleLoaded = !!tabsModule;
        
        // 检查标签页容器
        const tabsContainer = document.querySelector('.tabs-container');
        details.tabsContainerExists = !!tabsContainer;
        
        // 检查标签页按钮
        const tabButtons = document.querySelectorAll('.tab-btn');
        details.tabButtonsCount = tabButtons.length;
        
        const score = (details.tabsModuleLoaded ? 40 : 0) + 
                     (details.tabsContainerExists ? 30 : 0) + 
                     (details.tabButtonsCount > 0 ? 30 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试进度系统
     * @TEST 进度系统测试
     */
    async testProgressSystem(feature) {
        const details = {};
        
        // 检查进度模块
        const progressModule = window.ModuleExports && window.ModuleExports['progress.js'];
        details.progressModuleLoaded = !!progressModule;
        
        // 检查进度管理器
        const mainApp = window.mainApp;
        details.progressManagerAvailable = !!(mainApp && mainApp.progressManager);
        
        const score = (details.progressModuleLoaded ? 50 : 0) + 
                     (details.progressManagerAvailable ? 50 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试QA优化功能
     * @TEST QA优化功能测试
     */
    async testQAOptimization(feature) {
        const details = {};
        
        // 检查QA优化模块
        const qaModule = window.ModuleExports && window.ModuleExports['qa-optimization.js'];
        details.qaOptimizationModuleLoaded = !!qaModule;
        
        // 检查QA优化管理器
        const mainApp = window.mainApp;
        details.qaOptimizerAvailable = !!(mainApp && mainApp.qaOptimizer);
        
        const score = (details.qaOptimizationModuleLoaded ? 50 : 0) + 
                     (details.qaOptimizerAvailable ? 50 : 0);
        
        return {
            status: score >= 70 ? 'passed' : score >= 40 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试报告生成功能
     * @TEST 报告生成功能测试
     */
    async testReportGeneration(feature) {
        const details = {};
        
        // 检查报告生成模块
        const reportModule = window.ModuleExports && window.ModuleExports['report-generator.js'];
        details.reportGeneratorModuleLoaded = !!reportModule;
        
        // 检查报告生成器
        const mainApp = window.mainApp;
        details.reportGeneratorAvailable = !!(mainApp && mainApp.reportGenerator);
        
        const score = (details.reportGeneratorModuleLoaded ? 50 : 0) + 
                     (details.reportGeneratorAvailable ? 50 : 0);
        
        return {
            status: score >= 70 ? 'passed' : score >= 40 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试标签管理功能
     * @TEST 标签管理功能测试
     */
    async testTagManagement(feature) {
        const details = {};
        
        // 检查标签中心模块
        const tagModule = window.ModuleExports && window.ModuleExports['tag-center.js'];
        details.tagCenterModuleLoaded = !!tagModule;
        
        // 检查标签中心
        const mainApp = window.mainApp;
        details.tagCenterAvailable = !!(mainApp && mainApp.tagCenter);
        
        const score = (details.tagCenterModuleLoaded ? 50 : 0) + 
                     (details.tagCenterAvailable ? 50 : 0);
        
        return {
            status: score >= 70 ? 'passed' : score >= 40 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试模块化架构
     * @TEST 模块化架构测试
     */
    async testModularization(feature) {
        const details = {};
        
        // 检查模块数量
        const moduleCount = window.ModuleExports ? Object.keys(window.ModuleExports).length : 0;
        details.moduleCount = moduleCount;
        details.isModular = moduleCount > 10; // 至少10个模块
        
        // 检查模块加载器
        details.loaderExists = !!(window.ModuleLoader);
        
        const score = (details.isModular ? 60 : 0) + 
                     (details.loaderExists ? 40 : 0);
        
        return {
            status: score >= 90 ? 'passed' : score >= 60 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试依赖管理
     * @TEST 依赖管理测试
     */
    async testDependencyManagement(feature) {
        const details = {};
        
        // 检查依赖管理器模块
        const depModule = window.ModuleExports && window.ModuleExports['dependency-manager.js'];
        details.dependencyManagerModuleLoaded = !!depModule;
        
        // 检查依赖管理器实例
        const mainApp = window.mainApp;
        details.dependencyManagerAvailable = !!(mainApp && mainApp.dependencyManager);
        
        const score = (details.dependencyManagerModuleLoaded ? 50 : 0) + 
                     (details.dependencyManagerAvailable ? 50 : 0);
        
        return {
            status: score >= 80 ? 'passed' : score >= 50 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试事件系统
     * @TEST 事件系统测试
     */
    async testEventSystem(feature) {
        const details = {};
        
        // 检查事件总线模块
        const eventModule = window.ModuleExports && window.ModuleExports['event-bus.js'];
        details.eventBusModuleLoaded = !!eventModule;
        
        // 检查事件总线实例
        const mainApp = window.mainApp;
        details.eventBusAvailable = !!(mainApp && mainApp.eventBus);
        
        const score = (details.eventBusModuleLoaded ? 50 : 0) + 
                     (details.eventBusAvailable ? 50 : 0);
        
        return {
            status: score >= 80 ? 'passed' : score >= 50 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 测试服务容器
     * @TEST 服务容器测试
     */
    async testServiceContainer(feature) {
        const details = {};
        
        // 检查服务容器模块
        const serviceModule = window.ModuleExports && window.ModuleExports['service-container.js'];
        details.serviceContainerModuleLoaded = !!serviceModule;
        
        // 检查服务容器实例
        const mainApp = window.mainApp;
        details.serviceContainerAvailable = !!(mainApp && mainApp.serviceContainer);
        
        const score = (details.serviceContainerModuleLoaded ? 50 : 0) + 
                     (details.serviceContainerAvailable ? 50 : 0);
        
        return {
            status: score >= 80 ? 'passed' : score >= 50 ? 'partial' : 'failed',
            score: score,
            details: details
        };
    }

    /**
     * 计算总体评分
     * @UTIL 总体评分计算工具
     * @param {Object} summary - 统计摘要
     * @returns {number} 总体评分
     */
    calculateOverallScore(summary) {
        if (summary.totalFeatures === 0) return 0;
        
        const passedWeight = 1.0;
        const partialWeight = 0.6;
        const failedWeight = 0.0;
        
        const weightedScore = (summary.passedFeatures * passedWeight + 
                              summary.partialFeatures * partialWeight + 
                              summary.failedFeatures * failedWeight) / summary.totalFeatures;
        
        return Math.round(weightedScore * 100);
    }

    /**
     * 生成建议
     * @UTIL 建议生成工具
     * @param {Object} results - 测试结果
     * @returns {Array} 建议列表
     */
    generateRecommendations(results) {
        const recommendations = [];
        
        if (results.summary.overallScore < 80) {
            recommendations.push('系统整体兼容性需要改进，建议优先修复失败的功能');
        }
        
        if (results.summary.failedFeatures > 0) {
            recommendations.push(`有 ${results.summary.failedFeatures} 个功能完全不兼容，需要立即修复`);
        }
        
        if (results.summary.partialFeatures > 0) {
            recommendations.push(`有 ${results.summary.partialFeatures} 个功能部分兼容，建议进一步完善`);
        }
        
        if (results.summary.overallScore >= 90) {
            recommendations.push('系统兼容性良好，可以考虑部署');
        }
        
        return recommendations;
    }

    /**
     * 生成迁移状态报告
     * @UTIL 迁移状态报告生成工具
     * @param {Object} results - 测试结果
     * @returns {Object} 迁移状态报告
     */
    generateMigrationStatus(results) {
        const status = {
            overallStatus: 'unknown',
            readyForDeployment: false,
            criticalIssues: [],
            completedMigrations: [],
            pendingMigrations: []
        };
        
        // 确定总体状态
        if (results.summary.overallScore >= 90) {
            status.overallStatus = 'excellent';
            status.readyForDeployment = true;
        } else if (results.summary.overallScore >= 80) {
            status.overallStatus = 'good';
            status.readyForDeployment = true;
        } else if (results.summary.overallScore >= 60) {
            status.overallStatus = 'fair';
            status.readyForDeployment = false;
        } else {
            status.overallStatus = 'poor';
            status.readyForDeployment = false;
        }
        
        // 分析各类别状态
        for (const [categoryName, categoryResult] of Object.entries(results.categories)) {
            for (const result of categoryResult.results) {
                if (result.status === 'passed') {
                    status.completedMigrations.push(result.featureName);
                } else if (result.status === 'failed') {
                    status.criticalIssues.push(result.featureName);
                } else if (result.status === 'partial') {
                    status.pendingMigrations.push(result.featureName);
                }
            }
        }
        
        return status;
    }

    /**
     * 获取对比测试历史
     * @SERVICE 对比测试历史获取方法
     * @returns {Array} 历史测试结果
     */
    getComparisonHistory() {
        return Array.from(this.comparisonResults.entries()).map(([timestamp, results]) => ({
            timestamp: new Date(timestamp).toISOString(),
            results: results
        }));
    }

    /**
     * 清除对比测试历史
     * @SERVICE 对比测试历史清除方法
     */
    clearComparisonHistory() {
        this.comparisonResults.clear();
        console.log('🧹 对比测试历史已清除');
    }

    /**
     * 销毁对比测试器
     * @LIFECYCLE 对比测试器销毁方法
     */
    destroy() {
        this.clearComparisonHistory();
        this.featureMatrix.clear();
        this.functionalTester = null;
        this.performanceTester = null;
        console.log('🗑️ ComparisonTester 已销毁');
    }
}

// ==================== 全局实例 ====================
let globalComparisonTester = null;

/**
 * 获取全局功能对比测试器实例
 * @SERVICE 全局功能对比测试器获取函数
 * @returns {ComparisonTester} 功能对比测试器实例
 */
export function getComparisonTester() {
    if (!globalComparisonTester) {
        globalComparisonTester = new ComparisonTester();
    }
    return globalComparisonTester;
}

// ==================== 便捷函数 ====================

/**
 * 运行功能对比测试
 * @SERVICE 功能对比测试运行函数
 * @returns {Promise<Object>} 对比测试结果
 */
export async function runComparisonTest() {
    return await getComparisonTester().runFullComparisonTest();
}

/**
 * 快速兼容性检查
 * @SERVICE 快速兼容性检查函数
 * @returns {Object} 兼容性检查结果
 */
export function quickCompatibilityCheck() {
    const tester = getComparisonTester();
    
    return {
        moduleCount: window.ModuleExports ? Object.keys(window.ModuleExports).length : 0,
        mainAppInitialized: !!(window.mainApp),
        coreElementsPresent: {
            dropZone: !!document.getElementById('drop-zone'),
            fileInput: !!document.getElementById('file-input'),
            tabsContainer: !!document.querySelector('.tabs-container')
        },
        librariesLoaded: {
            echarts: !!(window.echarts),
            papa: !!(window.Papa)
        },
        timestamp: Date.now()
    };
}
