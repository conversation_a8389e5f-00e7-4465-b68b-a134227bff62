/**
 * ==================== GoMyHire 对话分析系统 - 事件总线系统 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Event Bus System
 * 版本: 2.0.0
 * 功能描述: 提供事件驱动架构的核心，实现事件发布订阅、中间件支持、错误处理、性能监控
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: utils.js (executeWithTimeout, generateUniqueId)
 * 间接依赖: 无
 * 被依赖: main.js, service-container.js, 以及所有需要事件通信的模块
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 1 (第二层，基础设施模块)
 * 加载时机: core (核心模块，立即加载)
 * 加载条件: 依赖utils.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 事件注册和订阅 (on, once, off)
 *   - 事件发布和广播 (emit, broadcast)
 *   - 中间件管理 (use, removeMiddleware)
 *   - 错误处理 (onError, 错误捕获和传播)
 *   - 性能监控 (metrics, 执行时间统计)
 *   - 事件过滤和限流
 * 导出接口: EventBus, getEventBus
 * 全局注册: window.ModuleExports['event-bus.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 事件名称、事件负载、处理器函数
 * 输出数据: 事件处理结果、性能指标、错误信息
 * 状态管理: 维护事件监听器映射、中间件栈、性能指标
 * 事件处理: 为整个系统提供事件驱动架构
 * 
 * @INTEGRATION (集成关系)
 * UI集成: 为UI事件(点击、输入等)提供统一处理
 * 服务集成: 与服务容器集成，支持服务间通信
 * 存储集成: 支持存储操作事件通知
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (维护事件监听器和中间件)
 * 加载性能: 快速 (无复杂初始化)
 * 运行时性能: 高效 (优化的事件分发机制，支持异步处理)
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// ==================== 事件总线类 ====================
/**
 * 事件总线类 - 系统核心事件管理器
 * @SERVICE 事件总线管理器
 */
class EventBus {
    constructor() {
        this.events = new Map(); // 事件处理器映射
        this.middlewares = []; // 中间件栈
        this.errorHandlers = new Map(); // 错误处理器
        this.metrics = {
            eventCount: 0,
            errorCount: 0,
            averageProcessingTime: 0,
            totalProcessingTime: 0
        };
        this.isDestroyed = false;

        console.log('🚌 EventBus 初始化完成');
    }

    /**
     * 注册事件处理器
     * @SERVICE 事件处理器注册方法
     * @param {string} event - 事件名称
     * @param {Function} handler - 处理器函数
     * @param {Object} options - 配置选项
     * @returns {string} 处理器ID
     */
    on(event, handler, options = {}) {
        if (this.isDestroyed) {
            throw new Error('EventBus已销毁，无法注册事件');
        }

        const {
            priority = 0,
            once = false,
            timeout = 30000,
            context = null
        } = options;

        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const handlerWrapper = {
            id: getUtils()?.generateUniqueId('handler') || 'handler_' + Date.now(),
            handler,
            priority,
            once,
            timeout,
            context,
            createdAt: Date.now()
        };

        // 按优先级插入
        const handlers = this.events.get(event);
        const insertIndex = handlers.findIndex(h => h.priority < priority);
        if (insertIndex === -1) {
            handlers.push(handlerWrapper);
        } else {
            handlers.splice(insertIndex, 0, handlerWrapper);
        }

        return handlerWrapper.id;
    }

    /**
     * 发射事件
     * @SERVICE 事件发射方法
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     * @param {Object} options - 配置选项
     * @returns {Promise<Array>} 处理结果数组
     */
    async emit(event, data = null, options = {}) {
        if (this.isDestroyed) return [];
        const startTime = performance.now();
        
        try {
            const { parallel = false, stopOnError = false } = options;
            const handlers = this.events.has(event) ? [...this.events.get(event)] : [];
            
            if (handlers.length === 0) {
                this.updateMetrics(startTime);
                return [];
            }

            let results = [];

            if (parallel) {
                // 并行执行所有处理器
                const promises = handlers.map(wrapper =>
                    this.executeHandler(wrapper, event, data)
                );
                results = await Promise.allSettled(promises);
            } else {
                // 串行执行处理器
                for (const wrapper of handlers) {
                    try {
                        const result = await this.executeHandler(wrapper, event, data);
                        results.push({ status: 'fulfilled', value: result });
                        
                        // 如果是一次性处理器，移除它
                        if (wrapper.once) {
                            this.off(event, wrapper.id);
                        }
                    } catch (error) {
                        results.push({ status: 'rejected', reason: error });
                        this.handleError(event, error);
                        
                        if (stopOnError) {
                            break;
                        }
                    }
                }
            }

            // 清理一次性处理器
            this.removeOnceHandlers(event);
            this.updateMetrics(startTime);
            
            return results;
        } catch (error) {
            this.handleError(event, error);
            this.updateMetrics(startTime);
            throw error;
        }
    }

    /**
     * 执行单个处理器
     * @UTIL 处理器执行工具
     * @param {Object} wrapper - 处理器包装对象
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     * @returns {Promise<any>} 执行结果
     */
    async executeHandler(wrapper, event, data) {
        const { handler, timeout, context } = wrapper;
        
        const executeWithContext = () => {
            if (context) {
                return handler.call(context, data, event);
            }
            return handler(data, event);
        };

        if (timeout > 0) {
            return await (getUtils()?.executeWithTimeout(executeWithContext, timeout) || executeWithContext());
        }
        
        return await executeWithContext();
    }

    /**
     * 移除事件处理器
     * @SERVICE 事件处理器移除方法
     * @param {string} event - 事件名称
     * @param {string} handlerId - 处理器ID
     * @returns {boolean} 是否移除成功
     */
    off(event, handlerId) {
        if (!this.events.has(event)) return false;

        const handlers = this.events.get(event);
        const index = handlers.findIndex(wrapper => wrapper.id === handlerId);
        
        if (index !== -1) {
            handlers.splice(index, 1);
            if (handlers.length === 0) {
                this.events.delete(event);
            }
            return true;
        }
        
        return false;
    }

    /**
     * 移除所有事件处理器
     * @SERVICE 事件处理器清空方法
     * @param {string} event - 事件名称
     */
    removeAllListeners(event) {
        if (event) {
            this.events.delete(event);
        } else {
            this.events.clear();
        }
    }

    /**
     * 注册一次性事件处理器
     * @SERVICE 一次性事件处理器注册方法
     * @param {string} event - 事件名称
     * @param {Function} handler - 处理器函数
     * @param {Object} options - 配置选项
     * @returns {string} 处理器ID
     */
    once(event, handler, options = {}) {
        return this.on(event, handler, { ...options, once: true });
    }

    /**
     * 添加中间件
     * @SERVICE 中间件添加方法
     * @param {Function} middleware - 中间件函数
     */
    use(middleware) {
        if (typeof middleware === 'function') {
            this.middlewares.push(middleware);
        }
    }

    /**
     * 超时执行包装器
     * @UTIL 超时执行工具
     * @param {Function} fn - 要执行的函数
     * @param {number} timeout - 超时时间
     * @returns {Promise} Promise对象
     */
    executeWithTimeout(fn, timeout) {
        // 优先使用utils.js中的实现，否则使用本地实现
        const utils = getUtils();
        if (utils && utils.executeWithTimeout) {
            return utils.executeWithTimeout(fn, timeout);
        }
        
        // 本地fallback实现
        return Promise.race([
            Promise.resolve(fn()),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error(`Handler timeout after ${timeout}ms`)), timeout)
            )
        ]);
    }

    /**
     * 移除一次性处理器
     * @UTIL 一次性处理器清理工具
     * @param {string} event - 事件名称
     */
    removeOnceHandlers(event) {
        if (!this.events.has(event)) return;

        const handlers = this.events.get(event);
        const remaining = handlers.filter(wrapper => !wrapper.once);

        if (remaining.length !== handlers.length) {
            this.events.set(event, remaining);
        }
    }

    /**
     * 更新性能指标
     * @UTIL 性能指标更新工具
     * @param {number} startTime - 开始时间
     */
    updateMetrics(startTime) {
        const processingTime = performance.now() - startTime;
        this.metrics.eventCount++;
        this.metrics.totalProcessingTime += processingTime;
        this.metrics.averageProcessingTime =
            this.metrics.totalProcessingTime / this.metrics.eventCount;
    }

    /**
     * 处理错误
     * @SERVICE 错误处理方法
     * @param {string} event - 事件名称
     * @param {Error} error - 错误对象
     */
    handleError(event, error) {
        this.metrics.errorCount++;
        console.error(`事件处理错误 (${event}):`, error);

        // 触发错误处理器
        if (this.errorHandlers.has(event)) {
            const errorHandler = this.errorHandlers.get(event);
            try {
                errorHandler(error, event);
            } catch (handlerError) {
                console.error('错误处理器执行失败:', handlerError);
            }
        }
    }

    /**
     * 注册错误处理器
     * @SERVICE 错误处理器注册方法
     * @param {string} event - 事件名称
     * @param {Function} handler - 错误处理器
     */
    onError(event, handler) {
        this.errorHandlers.set(event, handler);
    }

    /**
     * 获取统计信息
     * @SERVICE 统计信息获取方法
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            totalEvents: this.events.size,
            totalHandlers: 0,
            metrics: { ...this.metrics }
        };

        for (const handlers of this.events.values()) {
            stats.totalHandlers += handlers.length;
        }

        return stats;
    }

    /**
     * 获取事件列表
     * @SERVICE 事件列表获取方法
     * @returns {Array} 事件名称列表
     */
    getEventNames() {
        return Array.from(this.events.keys());
    }

    /**
     * 检查事件是否有处理器
     * @SERVICE 事件处理器检查方法
     * @param {string} event - 事件名称
     * @returns {boolean} 是否有处理器
     */
    hasListeners(event) {
        return this.events.has(event) && this.events.get(event).length > 0;
    }

    /**
     * 清理资源
     * @LIFECYCLE 资源清理方法
     */
    destroy() {
        this.events.clear();
        this.middlewares.length = 0;
        this.errorHandlers.clear();
        this.isDestroyed = true;
        console.log('🗑️ EventBus 已销毁');
    }
}

// ==================== 全局事件总线实例 ====================
let eventBusInstance = null;

/**
 * 获取全局事件总线实例
 * @SERVICE 全局事件总线获取函数
 */
function getEventBus() {
    if (!eventBusInstance) {
        eventBusInstance = new EventBus();
    }
    return eventBusInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['event-bus.js'] = {
    EventBus,
    getEventBus
};
