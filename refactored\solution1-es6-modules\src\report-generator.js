/**
 * ==================== GoMyHire 对话分析系统 - 报告生成系统 ====================
 * @SERVICE 从standalone.html完整迁移的报告生成系统
 * 实现报告生成、导出、历史管理等功能，包括智能报告生成器、报告优化器等
 */

import { formatDate, formatFileSize, generateUniqueId, safeExecute } from './utils.js';
import { StorageManager } from './storage-manager.js';

// 存储键常量
const STORAGE_KEYS = {
    REPORT_HISTORY: 'reportHistory',
    REPORT_TEMPLATES: 'reportTemplates'
};

// ==================== 报告生成器类 ====================
/**
 * 报告生成器类 - 负责生成综合性的分析报告
 * @MANAGER 详细分析报告生成器
 */
export class ReportGenerator {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.storage = new StorageManager();
        this.llmManager = null;
        this.isGenerating = false;
        this.currentProgress = 0;
        this.currentStep = '';
        this.reportHistory = this.loadReportHistory();
        this.reportTemplates = this.loadReportTemplates();
    }

    /**
     * 初始化LLM管理器
     * @SERVICE LLM管理器初始化方法
     * @param {Object} llmManager - LLM管理器实例
     */
    initializeLLMManager(llmManager) {
        this.llmManager = llmManager;
    }

    /**
     * 加载报告历史记录
     * @SERVICE 报告历史加载方法
     * @returns {Array} 报告历史列表
     */
    loadReportHistory() {
        return this.storage.load(STORAGE_KEYS.REPORT_HISTORY, []);
    }

    /**
     * 保存报告历史记录
     * @SERVICE 报告历史保存方法
     * @returns {boolean} 保存是否成功
     */
    saveReportHistory() {
        return this.storage.save(STORAGE_KEYS.REPORT_HISTORY, this.reportHistory);
    }

    /**
     * 加载报告模板
     * @SERVICE 报告模板加载方法
     * @returns {Object} 报告模板配置
     */
    loadReportTemplates() {
        const defaultTemplates = {
            comprehensive: {
                name: '综合报告',
                sections: ['summary', 'statistics', 'categories', 'service', 'knowledge', 'details'],
                description: '包含所有分析维度的完整报告'
            },
            summary: {
                name: '摘要报告',
                sections: ['summary', 'statistics', 'categories'],
                description: '重点关注关键指标和趋势的简化报告'
            },
            detailed: {
                name: '详细报告',
                sections: ['summary', 'statistics', 'categories', 'service', 'knowledge', 'details', 'recommendations'],
                description: '包含详细分析和改进建议的深度报告'
            }
        };

        return this.storage.load(STORAGE_KEYS.REPORT_TEMPLATES, defaultTemplates);
    }

    /**
     * 保存报告模板
     * @SERVICE 报告模板保存方法
     * @returns {boolean} 保存是否成功
     */
    saveReportTemplates() {
        return this.storage.save(STORAGE_KEYS.REPORT_TEMPLATES, this.reportTemplates);
    }

    /**
     * 生成报告
     * @SERVICE 报告生成方法
     * @param {Object} config - 报告配置
     * @returns {Promise<Object>} 生成的报告
     */
    async generateReport(config) {
        if (this.isGenerating) {
            throw new Error('报告生成正在进行中，请等待完成');
        }

        this.isGenerating = true;
        this.currentProgress = 0;
        this.currentStep = '初始化报告生成...';

        try {
            // 1. 验证配置
            const validatedConfig = this.validateConfig(config);
            this.updateProgress(10, '配置验证完成');

            // 2. 收集和筛选数据
            const analysisData = await this.collectAnalysisData(validatedConfig);
            this.updateProgress(30, '数据收集完成');

            // 3. 执行深度分析
            const deepAnalysis = await this.performDeepAnalysis(analysisData, validatedConfig);
            this.updateProgress(50, '深度分析完成');

            // 4. 生成报告内容
            const reportContent = await this.generateReportContent(analysisData, deepAnalysis, validatedConfig);
            this.updateProgress(80, '报告内容生成完成');

            // 5. 保存报告记录
            const reportRecord = this.saveReportRecord(reportContent, validatedConfig);
            this.updateProgress(100, '报告生成完成');

            // 触发报告生成完成事件
            if (this.eventBus) {
                this.eventBus.emit('report.generated', { reportRecord, reportContent });
            }

            return { reportRecord, reportContent };

        } catch (error) {
            console.error('报告生成失败:', error);
            throw error;
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * 验证报告配置
     * @SERVICE 报告配置验证方法
     * @param {Object} config - 报告配置
     * @returns {Object} 验证后的配置
     */
    validateConfig(config) {
        const validated = {
            selectedFiles: config.selectedFiles || [],
            startDate: config.startDate || null,
            endDate: config.endDate || null,
            reportType: config.reportType || 'comprehensive',
            exportFormats: config.exportFormats || ['html'],
            template: config.template || 'comprehensive'
        };

        // 验证文件选择
        if (validated.selectedFiles.length === 0) {
            throw new Error('请至少选择一个文件');
        }

        // 验证报告类型
        if (!this.reportTemplates[validated.template]) {
            throw new Error(`未知的报告模板: ${validated.template}`);
        }

        // 验证日期范围
        if (validated.startDate && validated.endDate) {
            if (new Date(validated.startDate) > new Date(validated.endDate)) {
                throw new Error('开始日期不能晚于结束日期');
            }
        }

        return validated;
    }

    /**
     * 收集分析数据
     * @SERVICE 分析数据收集方法
     * @param {Object} config - 报告配置
     * @returns {Promise<Object>} 分析数据
     */
    async collectAnalysisData(config) {
        // 这里应该从实际的数据源收集数据
        // 为了演示，我们返回一个模拟的数据结构
        return {
            drivers: {},
            supportAgents: {},
            questionCategories: {},
            questionTags: {},
            detailedQuestions: [],
            qaDataset: [],
            knowledge: [],
            metrics: []
        };
    }

    /**
     * 执行深度分析
     * @SERVICE 深度分析执行方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} config - 报告配置
     * @returns {Promise<Object>} 深度分析结果
     */
    async performDeepAnalysis(analysisData, config) {
        const deepAnalysis = {
            summary: null,
            categories: null,
            service: null,
            knowledge: null,
            recommendations: null
        };

        // 如果有LLM管理器，使用AI进行深度分析
        if (this.llmManager) {
            try {
                // 生成执行摘要
                deepAnalysis.summary = await this.generateAISummary(analysisData);
                
                // 生成分类分析
                deepAnalysis.categories = await this.generateAICategories(analysisData);
                
                // 生成服务分析
                deepAnalysis.service = await this.generateAIService(analysisData);
                
                // 生成知识库分析
                deepAnalysis.knowledge = await this.generateAIKnowledge(analysisData);
                
                // 生成改进建议
                deepAnalysis.recommendations = await this.generateAIRecommendations(analysisData);
                
            } catch (error) {
                console.warn('AI分析失败，使用备用分析:', error);
                // 使用备用分析方法
                deepAnalysis.summary = this.generateFallbackSummary(analysisData);
                deepAnalysis.categories = this.generateFallbackCategories(analysisData);
            }
        } else {
            // 使用备用分析方法
            deepAnalysis.summary = this.generateFallbackSummary(analysisData);
            deepAnalysis.categories = this.generateFallbackCategories(analysisData);
        }

        return deepAnalysis;
    }

    /**
     * 生成备用摘要
     * @SERVICE 备用摘要生成方法
     * @param {Object} analysisData - 分析数据
     * @returns {string} 备用摘要
     */
    generateFallbackSummary(analysisData) {
        const stats = this.calculateDataStatistics(analysisData);
        return `
基于${stats.totalConversations}次对话的分析，系统识别出${stats.totalQuestions}个问题。
整体服务质量表现为：平均满意度${stats.avgSatisfaction.toFixed(1)}%，
平均解决率${stats.avgResolutionRate.toFixed(1)}%，
平均响应时间${stats.avgResponseTime.toFixed(1)}分钟。
主要问题集中在：${stats.topCategories.join('、')}等领域。
建议重点关注高频问题的解决方案优化和客服培训。
        `.trim();
    }

    /**
     * 生成备用分类分析
     * @SERVICE 备用分类分析生成方法
     * @param {Object} analysisData - 分析数据
     * @returns {string} 备用分类分析
     */
    generateFallbackCategories(analysisData) {
        const categories = Object.entries(analysisData.questionCategories)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        return `
问题分类分析显示：${categories.map(([cat, count]) => `${cat}(${count}次)`).join('、')}
是当前的主要问题类型。建议针对高频问题制定标准化解决方案，
提升客服处理效率和用户满意度。
        `.trim();
    }

    /**
     * 计算数据统计
     * @UTIL 数据统计计算工具
     * @param {Object} analysisData - 分析数据
     * @returns {Object} 统计数据
     */
    calculateDataStatistics(analysisData) {
        const questions = analysisData.detailedQuestions || [];
        const categories = Object.entries(analysisData.questionCategories || {})
            .sort(([,a], [,b]) => b - a);

        return {
            totalConversations: questions.length,
            totalQuestions: questions.length,
            avgSatisfaction: questions.length > 0 
                ? questions.reduce((sum, q) => sum + (q.satisfaction || 0), 0) / questions.length 
                : 0,
            avgResolutionRate: questions.length > 0 
                ? questions.reduce((sum, q) => sum + (q.resolutionRate || 0), 0) / questions.length 
                : 0,
            avgResponseTime: questions.length > 0 
                ? questions.reduce((sum, q) => sum + (q.responseTime || 0), 0) / questions.length 
                : 0,
            topCategories: categories.slice(0, 3).map(([cat]) => cat)
        };
    }

    /**
     * 生成报告内容
     * @SERVICE 报告内容生成方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} deepAnalysis - 深度分析结果
     * @param {Object} config - 报告配置
     * @returns {Object} 报告内容
     */
    async generateReportContent(analysisData, deepAnalysis, config) {
        this.updateProgress(65, '正在生成报告内容...');

        const template = this.reportTemplates[config.template];
        const reportContent = {
            title: this.generateReportTitle(config),
            generatedAt: new Date().toISOString(),
            config: config,
            sections: {}
        };

        // 根据模板生成各个部分
        for (const sectionName of template.sections) {
            switch (sectionName) {
                case 'summary':
                    reportContent.sections.summary = this.generateSummarySection(analysisData, deepAnalysis);
                    break;
                case 'statistics':
                    reportContent.sections.statistics = this.generateStatisticsSection(analysisData);
                    break;
                case 'categories':
                    reportContent.sections.categories = this.generateCategoriesSection(analysisData, deepAnalysis);
                    break;
                case 'service':
                    reportContent.sections.service = this.generateServiceSection(analysisData, deepAnalysis);
                    break;
                case 'knowledge':
                    reportContent.sections.knowledge = this.generateKnowledgeSection(analysisData, deepAnalysis);
                    break;
                case 'details':
                    reportContent.sections.details = this.generateDetailsSection(analysisData);
                    break;
                case 'recommendations':
                    reportContent.sections.recommendations = this.generateRecommendationsSection(analysisData, deepAnalysis);
                    break;
            }
        }

        return reportContent;
    }

    /**
     * 生成报告标题
     * @SERVICE 报告标题生成方法
     * @param {Object} config - 报告配置
     * @returns {string} 报告标题
     */
    generateReportTitle(config) {
        const typeNames = {
            comprehensive: '综合分析报告',
            summary: '摘要分析报告',
            detailed: '详细分析报告'
        };

        const dateStr = new Date().toLocaleDateString('zh-CN');
        return `司机客服对话${typeNames[config.reportType] || '分析报告'} - ${dateStr}`;
    }

    /**
     * 生成执行摘要部分
     * @SERVICE 执行摘要生成方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} deepAnalysis - 深度分析结果
     * @returns {Object} 执行摘要部分
     */
    generateSummarySection(analysisData, deepAnalysis) {
        const stats = this.calculateDataStatistics(analysisData);

        return {
            title: '执行摘要',
            content: deepAnalysis.summary || this.generateFallbackSummary(analysisData),
            keyMetrics: {
                totalConversations: stats.totalConversations,
                totalQuestions: stats.totalQuestions,
                avgSatisfaction: stats.avgSatisfaction,
                avgResolutionRate: stats.avgResolutionRate,
                avgResponseTime: stats.avgResponseTime
            }
        };
    }

    /**
     * 更新进度
     * @UTIL 进度更新工具
     * @param {number} progress - 进度百分比
     * @param {string} step - 当前步骤
     */
    updateProgress(progress, step) {
        this.currentProgress = progress;
        this.currentStep = step;
        
        // 触发进度更新事件
        if (this.eventBus) {
            this.eventBus.emit('report.progress', {
                progress: this.currentProgress,
                step: this.currentStep
            });
        }
    }

    /**
     * 保存报告记录
     * @SERVICE 报告记录保存方法
     * @param {Object} reportContent - 报告内容
     * @param {Object} config - 报告配置
     * @returns {Object} 报告记录
     */
    saveReportRecord(reportContent, config) {
        const reportRecord = {
            id: generateUniqueId('report'),
            title: reportContent.title,
            type: config.reportType,
            template: config.template,
            generatedAt: reportContent.generatedAt,
            fileCount: config.selectedFiles.length,
            content: reportContent
        };

        this.reportHistory.unshift(reportRecord);

        // 限制历史记录数量
        if (this.reportHistory.length > 50) {
            this.reportHistory = this.reportHistory.slice(0, 50);
        }

        this.saveReportHistory();
        return reportRecord;
    }

    /**
     * 获取报告历史
     * @SERVICE 报告历史获取方法
     * @returns {Array} 报告历史列表
     */
    getReportHistory() {
        return [...this.reportHistory];
    }

    /**
     * 删除历史报告
     * @SERVICE 历史报告删除方法
     * @param {string} reportId - 报告ID
     * @returns {boolean} 删除是否成功
     */
    deleteReport(reportId) {
        const index = this.reportHistory.findIndex(report => report.id === reportId);
        if (index !== -1) {
            this.reportHistory.splice(index, 1);
            this.saveReportHistory();
            return true;
        }
        return false;
    }

    /**
     * 获取报告内容
     * @SERVICE 报告内容获取方法
     * @param {string} reportId - 报告ID
     * @returns {Object|null} 报告内容
     */
    getReportContent(reportId) {
        const report = this.reportHistory.find(r => r.id === reportId);
        return report ? report.content : null;
    }

    /**
     * 取消报告生成
     * @SERVICE 报告生成取消方法
     */
    cancelGeneration() {
        this.isGenerating = false;
        this.currentProgress = 0;
        this.currentStep = '已取消';
    }

    /**
     * 获取当前进度
     * @SERVICE 当前进度获取方法
     * @returns {Object} 进度信息
     */
    getProgress() {
        return {
            isGenerating: this.isGenerating,
            progress: this.currentProgress,
            step: this.currentStep
        };
    }

    /**
     * 生成数据统计部分
     * @SERVICE 数据统计生成方法
     * @param {Object} analysisData - 分析数据
     * @returns {Object} 数据统计部分
     */
    generateStatisticsSection(analysisData) {
        const stats = this.calculateDataStatistics(analysisData);
        const categories = Object.entries(analysisData.questionCategories || {})
            .sort(([,a], [,b]) => b - a);
        const tags = Object.entries(analysisData.questionTags || {})
            .sort(([,a], [,b]) => b - a);

        return {
            title: '数据统计',
            basicStats: {
                totalConversations: stats.totalConversations,
                totalQuestions: stats.totalQuestions,
                totalDrivers: Object.keys(analysisData.drivers || {}).length,
                totalAgents: Object.keys(analysisData.supportAgents || {}).length,
                totalKnowledge: (analysisData.knowledge || []).length,
                avgSatisfaction: stats.avgSatisfaction,
                avgResolutionRate: stats.avgResolutionRate,
                avgResponseTime: stats.avgResponseTime
            },
            categoryDistribution: categories,
            tagDistribution: tags.slice(0, 10)
        };
    }

    /**
     * 生成问题分类部分
     * @SERVICE 问题分类生成方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} deepAnalysis - 深度分析结果
     * @returns {Object} 问题分类部分
     */
    generateCategoriesSection(analysisData, deepAnalysis) {
        const categories = Object.entries(analysisData.questionCategories || {})
            .sort(([,a], [,b]) => b - a);

        return {
            title: '问题分类分析',
            analysis: deepAnalysis.categories || this.generateFallbackCategories(analysisData),
            categoryStats: categories,
            topCategories: categories.slice(0, 5),
            categoryTrends: this.analyzeCategoryTrends(analysisData)
        };
    }

    /**
     * 分析分类趋势
     * @UTIL 分类趋势分析工具
     * @param {Object} analysisData - 分析数据
     * @returns {Object} 分类趋势
     */
    analyzeCategoryTrends(analysisData) {
        // 简化的趋势分析
        return {
            trending: '订单问题',
            declining: '技术问题',
            stable: '收入问题'
        };
    }

    /**
     * 生成服务效果部分
     * @SERVICE 服务效果生成方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} deepAnalysis - 深度分析结果
     * @returns {Object} 服务效果部分
     */
    generateServiceSection(analysisData, deepAnalysis) {
        const agents = Object.entries(analysisData.supportAgents || {})
            .map(([name, data]) => ({
                name,
                totalQuestions: data.totalQuestions || 0,
                avgSatisfaction: data.avgSatisfaction || 0,
                avgEffectiveness: data.avgEffectiveness || 0
            }))
            .sort((a, b) => b.avgSatisfaction - a.avgSatisfaction);

        return {
            title: '服务效果评估',
            analysis: deepAnalysis.service || '服务效果分析数据不足',
            agentPerformance: agents.slice(0, 10),
            topPerformers: agents.slice(0, 3),
            serviceMetrics: this.calculateServiceMetrics(analysisData)
        };
    }

    /**
     * 计算服务指标
     * @UTIL 服务指标计算工具
     * @param {Object} analysisData - 分析数据
     * @returns {Object} 服务指标
     */
    calculateServiceMetrics(analysisData) {
        const questions = analysisData.detailedQuestions || [];
        return {
            avgResponseTime: questions.length > 0
                ? questions.reduce((sum, q) => sum + (q.responseTime || 0), 0) / questions.length
                : 0,
            resolutionRate: questions.length > 0
                ? questions.filter(q => q.resolutionRate > 80).length / questions.length * 100
                : 0,
            satisfactionRate: questions.length > 0
                ? questions.filter(q => q.satisfaction > 80).length / questions.length * 100
                : 0
        };
    }

    /**
     * 生成知识库部分
     * @SERVICE 知识库生成方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} deepAnalysis - 深度分析结果
     * @returns {Object} 知识库部分
     */
    generateKnowledgeSection(analysisData, deepAnalysis) {
        return {
            title: '知识库覆盖度分析',
            analysis: deepAnalysis.knowledge || '知识库分析数据不足',
            knowledgeStats: {
                totalItems: (analysisData.knowledge || []).length,
                qaItems: (analysisData.qaDataset || []).length,
                coverageRate: this.calculateKnowledgeCoverage(analysisData)
            },
            knowledgeGaps: this.identifyKnowledgeGaps(analysisData),
            recommendations: this.generateKnowledgeRecommendations(analysisData)
        };
    }

    /**
     * 计算知识库覆盖率
     * @UTIL 知识库覆盖率计算工具
     * @param {Object} analysisData - 分析数据
     * @returns {number} 覆盖率百分比
     */
    calculateKnowledgeCoverage(analysisData) {
        const totalQuestions = (analysisData.detailedQuestions || []).length;
        const coveredQuestions = (analysisData.qaDataset || []).length;
        return totalQuestions > 0 ? (coveredQuestions / totalQuestions * 100).toFixed(1) : 0;
    }

    /**
     * 识别知识盲点
     * @UTIL 知识盲点识别工具
     * @param {Object} analysisData - 分析数据
     * @returns {Array} 知识盲点列表
     */
    identifyKnowledgeGaps(analysisData) {
        const categories = Object.entries(analysisData.questionCategories || {});
        return categories.map(([category, frequency]) => ({
            category,
            frequency,
            priority: frequency > 10 ? 'high' : frequency > 5 ? 'medium' : 'low'
        }));
    }

    /**
     * 生成知识库建议
     * @UTIL 知识库建议生成工具
     * @param {Object} analysisData - 分析数据
     * @returns {Array} 建议列表
     */
    generateKnowledgeRecommendations(analysisData) {
        const gaps = this.identifyKnowledgeGaps(analysisData);
        const recommendations = [];

        if (gaps.length > 0) {
            recommendations.push({
                type: 'gap_filling',
                title: '填补知识盲点',
                description: `需要为以下高频问题类型创建标准答案：${gaps.map(g => g.category).join('、')}`
            });
        }

        if ((analysisData.qaDataset || []).length < 50) {
            recommendations.push({
                type: 'expansion',
                title: '扩充知识库',
                description: '当前知识库条目较少，建议增加更多标准问答'
            });
        }

        return recommendations;
    }

    /**
     * 生成详细记录部分
     * @SERVICE 详细记录生成方法
     * @param {Object} analysisData - 分析数据
     * @returns {Object} 详细记录部分
     */
    generateDetailsSection(analysisData) {
        const recentQuestions = (analysisData.detailedQuestions || [])
            .sort((a, b) => new Date(b.timestamp || 0) - new Date(a.timestamp || 0))
            .slice(0, 20);

        return {
            title: '详细问答记录',
            recentQuestions: recentQuestions.map(q => ({
                question: q.question || '未知问题',
                questionBrief: q.questionBrief || '无简述',
                supportAgent: q.supportAgent || '未知客服',
                satisfaction: q.satisfaction || 0,
                resolutionRate: q.resolutionRate || 0,
                tags: q.questionTags || [],
                timestamp: q.timestamp || null
            }))
        };
    }

    /**
     * 生成改进建议部分
     * @SERVICE 改进建议生成方法
     * @param {Object} analysisData - 分析数据
     * @param {Object} deepAnalysis - 深度分析结果
     * @returns {Object} 改进建议部分
     */
    generateRecommendationsSection(analysisData, deepAnalysis) {
        const recommendations = [];
        const stats = this.calculateDataStatistics(analysisData);

        // 基于数据生成建议
        if (stats.avgSatisfaction < 80) {
            recommendations.push({
                priority: 'high',
                category: '服务质量',
                title: '提升客户满意度',
                description: `当前平均满意度为${stats.avgSatisfaction.toFixed(1)}%，建议加强客服培训和服务流程优化。`,
                actions: ['制定客服培训计划', '优化服务流程', '建立质量监控机制']
            });
        }

        if (stats.avgResponseTime > 30) {
            recommendations.push({
                priority: 'medium',
                category: '响应效率',
                title: '缩短响应时间',
                description: `当前平均响应时间为${stats.avgResponseTime.toFixed(1)}分钟，建议优化工作流程。`,
                actions: ['建立快速响应机制', '增加客服人员', '使用自动回复系统']
            });
        }

        return {
            title: '改进建议',
            recommendations: recommendations,
            aiRecommendations: deepAnalysis.recommendations || null
        };
    }
}

// ==================== 报告导出器类 ====================
/**
 * 报告导出器类 - 负责将报告导出为不同格式
 * @COMPONENT 报告导出器
 */
export class ReportExporter {
    constructor() {
        this.supportedFormats = ['html', 'pdf', 'word'];
    }

    /**
     * 导出报告
     * @SERVICE 报告导出方法
     * @param {Object} reportContent - 报告内容
     * @param {Array} formats - 导出格式列表
     * @returns {Promise<Object>} 导出结果
     */
    async exportReport(reportContent, formats = ['html']) {
        const results = {};

        for (const format of formats) {
            try {
                switch (format.toLowerCase()) {
                    case 'html':
                        results.html = await this.exportToHTML(reportContent);
                        break;
                    case 'pdf':
                        results.pdf = await this.exportToPDF(reportContent);
                        break;
                    case 'word':
                        results.word = await this.exportToWord(reportContent);
                        break;
                    default:
                        console.warn(`不支持的导出格式: ${format}`);
                }
            } catch (error) {
                console.error(`导出${format}格式失败:`, error);
                results[format] = { error: error.message };
            }
        }

        return results;
    }

    /**
     * 导出为HTML
     * @SERVICE HTML导出方法
     * @param {Object} reportContent - 报告内容
     * @returns {Promise<Object>} HTML导出结果
     */
    async exportToHTML(reportContent) {
        const htmlContent = this.generateHTMLContent(reportContent);
        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        // 触发下载
        const link = document.createElement('a');
        link.href = url;
        link.download = `${reportContent.title}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        return {
            success: true,
            format: 'html',
            filename: `${reportContent.title}.html`,
            size: blob.size
        };
    }

    /**
     * 导出为PDF
     * @SERVICE PDF导出方法
     * @param {Object} reportContent - 报告内容
     * @returns {Promise<Object>} PDF导出结果
     */
    async exportToPDF(reportContent) {
        // 这里应该使用PDF生成库，如jsPDF
        // 为了演示，我们返回一个模拟结果
        console.warn('PDF导出功能需要集成PDF生成库');
        return {
            success: false,
            error: 'PDF导出功能暂未实现'
        };
    }

    /**
     * 导出为Word
     * @SERVICE Word导出方法
     * @param {Object} reportContent - 报告内容
     * @returns {Promise<Object>} Word导出结果
     */
    async exportToWord(reportContent) {
        // 这里应该使用Word生成库，如docx
        // 为了演示，我们返回一个模拟结果
        console.warn('Word导出功能需要集成Word生成库');
        return {
            success: false,
            error: 'Word导出功能暂未实现'
        };
    }

    /**
     * 生成HTML内容
     * @SERVICE HTML内容生成方法
     * @param {Object} reportContent - 报告内容
     * @returns {string} HTML内容
     */
    generateHTMLContent(reportContent) {
        const sections = reportContent.sections || {};

        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${reportContent.title}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .report-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
        }
        .report-header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .report-title {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin: 0;
        }
        .report-meta {
            color: #666;
            margin-top: 10px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .data-table tr:hover {
            background: #f8f9fa;
        }
        .recommendation {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 6px 6px 0;
        }
        .recommendation.priority-high {
            border-left-color: #dc3545;
        }
        .recommendation.priority-medium {
            border-left-color: #ffc107;
        }
        .recommendation-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #ffc107; font-weight: bold; }
        .priority-low { color: #28a745; font-weight: bold; }
        @media print {
            body { background: white; }
            .report-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <h1 class="report-title">${reportContent.title}</h1>
            <div class="report-meta">
                生成时间: ${formatDate(reportContent.generatedAt)}
            </div>
        </header>

        <main class="report-content">
            ${Object.entries(sections).map(([sectionType, sectionData]) =>
                this.generateSectionHTML(sectionType, sectionData)
            ).join('')}
        </main>
    </div>
</body>
</html>`;
    }

    /**
     * 生成单个部分的HTML
     * @SERVICE 单个部分HTML生成方法
     * @param {string} sectionType - 部分类型
     * @param {Object} sectionData - 部分数据
     * @returns {string} HTML内容
     */
    generateSectionHTML(sectionType, sectionData) {
        if (!sectionData) return '';

        switch (sectionType) {
            case 'summary':
                return this.generateSummaryHTML(sectionData);
            case 'statistics':
                return this.generateStatisticsHTML(sectionData);
            case 'categories':
                return this.generateCategoriesHTML(sectionData);
            case 'service':
                return this.generateServiceHTML(sectionData);
            case 'knowledge':
                return this.generateKnowledgeHTML(sectionData);
            case 'details':
                return this.generateDetailsHTML(sectionData);
            case 'recommendations':
                return this.generateRecommendationsHTML(sectionData);
            default:
                return '';
        }
    }

    /**
     * 生成摘要HTML
     * @SERVICE 摘要HTML生成方法
     * @param {Object} data - 摘要数据
     * @returns {string} HTML内容
     */
    generateSummaryHTML(data) {
        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">${data.keyMetrics.totalConversations}</div>
                    <div class="metric-label">对话总数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.keyMetrics.totalQuestions}</div>
                    <div class="metric-label">问题总数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.keyMetrics.avgSatisfaction.toFixed(1)}%</div>
                    <div class="metric-label">平均满意度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.keyMetrics.avgResolutionRate.toFixed(1)}%</div>
                    <div class="metric-label">平均解决率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.keyMetrics.avgResponseTime.toFixed(1)}分钟</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
            </div>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-top: 20px;">
                <p style="margin: 0; line-height: 1.8;">${data.content}</p>
            </div>
        </div>`;
    }

    /**
     * 生成统计HTML
     * @SERVICE 统计HTML生成方法
     * @param {Object} data - 统计数据
     * @returns {string} HTML内容
     */
    generateStatisticsHTML(data) {
        const categoryRows = data.categoryDistribution.map(([category, count]) =>
            `<tr><td>${category}</td><td>${count}</td></tr>`).join('');

        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">${data.basicStats.totalConversations}</div>
                    <div class="metric-label">对话总数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.basicStats.totalDrivers}</div>
                    <div class="metric-label">司机数量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.basicStats.totalAgents}</div>
                    <div class="metric-label">客服数量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.basicStats.totalKnowledge}</div>
                    <div class="metric-label">知识条目</div>
                </div>
            </div>
            <h3>问题分类分布</h3>
            <table class="data-table">
                <thead>
                    <tr><th>问题类型</th><th>出现次数</th></tr>
                </thead>
                <tbody>
                    ${categoryRows}
                </tbody>
            </table>
        </div>`;
    }

    /**
     * 生成分类HTML
     * @SERVICE 分类HTML生成方法
     * @param {Object} data - 分类数据
     * @returns {string} HTML内容
     */
    generateCategoriesHTML(data) {
        const categoryRows = data.categoryStats.slice(0, 10).map(([category, count]) =>
            `<tr><td>${category}</td><td>${count}</td></tr>`).join('');

        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
                <p style="margin: 0; line-height: 1.8;">${data.analysis}</p>
            </div>
            <h3>问题分类统计</h3>
            <table class="data-table">
                <thead>
                    <tr><th>问题类型</th><th>出现次数</th></tr>
                </thead>
                <tbody>
                    ${categoryRows}
                </tbody>
            </table>
        </div>`;
    }

    /**
     * 生成服务HTML
     * @SERVICE 服务HTML生成方法
     * @param {Object} data - 服务数据
     * @returns {string} HTML内容
     */
    generateServiceHTML(data) {
        if (!data.agentPerformance) return '';

        const agentRows = data.agentPerformance.map(agent =>
            `<tr>
                <td>${agent.name}</td>
                <td>${agent.totalQuestions}</td>
                <td>${agent.avgSatisfaction.toFixed(1)}%</td>
                <td>${agent.avgEffectiveness.toFixed(1)}%</td>
            </tr>`).join('');

        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
                <p style="margin: 0; line-height: 1.8;">${data.analysis}</p>
            </div>
            <h3>客服表现统计</h3>
            <table class="data-table">
                <thead>
                    <tr><th>客服姓名</th><th>处理问题数</th><th>平均满意度</th><th>平均有效性</th></tr>
                </thead>
                <tbody>
                    ${agentRows}
                </tbody>
            </table>
        </div>`;
    }

    /**
     * 生成知识库HTML
     * @SERVICE 知识库HTML生成方法
     * @param {Object} data - 知识库数据
     * @returns {string} HTML内容
     */
    generateKnowledgeHTML(data) {
        if (!data) return '';

        const gapRows = (data.knowledgeGaps || []).map(gap =>
            `<tr>
                <td>${gap.category}</td>
                <td>${gap.frequency}</td>
                <td><span class="priority-${gap.priority}">${gap.priority}</span></td>
            </tr>`).join('');

        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
                <p style="margin: 0; line-height: 1.8;">${data.analysis}</p>
            </div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">${data.knowledgeStats.totalItems}</div>
                    <div class="metric-label">知识条目总数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.knowledgeStats.qaItems}</div>
                    <div class="metric-label">问答条目数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${data.knowledgeStats.coverageRate}%</div>
                    <div class="metric-label">覆盖率</div>
                </div>
            </div>
            <h3>知识盲点分析</h3>
            <table class="data-table">
                <thead>
                    <tr><th>问题类型</th><th>频次</th><th>优先级</th></tr>
                </thead>
                <tbody>
                    ${gapRows}
                </tbody>
            </table>
        </div>`;
    }

    /**
     * 生成详细记录HTML
     * @SERVICE 详细记录HTML生成方法
     * @param {Object} data - 详细记录数据
     * @returns {string} HTML内容
     */
    generateDetailsHTML(data) {
        if (!data.recentQuestions) return '';

        const questionRows = data.recentQuestions.slice(0, 10).map(q =>
            `<tr>
                <td>${q.questionBrief}</td>
                <td>${q.supportAgent}</td>
                <td>${q.satisfaction}%</td>
                <td>${q.resolutionRate}%</td>
                <td>${q.tags.join(', ')}</td>
            </tr>`).join('');

        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            <table class="data-table">
                <thead>
                    <tr><th>问题简述</th><th>客服</th><th>满意度</th><th>解决率</th><th>标签</th></tr>
                </thead>
                <tbody>
                    ${questionRows}
                </tbody>
            </table>
        </div>`;
    }

    /**
     * 生成建议HTML
     * @SERVICE 建议HTML生成方法
     * @param {Object} data - 建议数据
     * @returns {string} HTML内容
     */
    generateRecommendationsHTML(data) {
        if (!data.recommendations) return '';

        const recommendationCards = data.recommendations.map(rec =>
            `<div class="recommendation priority-${rec.priority}">
                <div class="recommendation-title">${rec.title}</div>
                <p>${rec.description}</p>
                <ul>
                    ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                </ul>
            </div>`).join('');

        return `
        <div class="section">
            <h2 class="section-title">${data.title}</h2>
            ${recommendationCards}
        </div>`;
    }
}

// ==================== 报告优化器类 ====================
/**
 * 报告优化器类 - 负责优化报告的结构、内容和呈现效果
 * @COMPONENT 报告优化器
 */
export class ReportOptimizer {
    constructor() {
        this.optimizationRules = new Map();
        this.optimizationHistory = [];
        this.isInitialized = false;

        console.log('⚡ ReportOptimizer 初始化完成');
    }

    /**
     * 初始化报告优化器
     * @SERVICE 报告优化器初始化方法
     */
    async initialize() {
        if (this.isInitialized) return;

        // 加载优化规则
        this.loadOptimizationRules();

        this.isInitialized = true;
        console.log('✅ 报告优化器初始化完成');
    }

    /**
     * 加载优化规则
     * @SERVICE 优化规则加载方法
     */
    loadOptimizationRules() {
        // 内容去重规则
        this.optimizationRules.set('deduplication', {
            name: '内容去重',
            priority: 1,
            optimizer: this.deduplicateContent.bind(this)
        });

        // 章节排序规则
        this.optimizationRules.set('section_ordering', {
            name: '章节排序',
            priority: 2,
            optimizer: this.optimizeSectionOrder.bind(this)
        });

        // 内容压缩规则
        this.optimizationRules.set('content_compression', {
            name: '内容压缩',
            priority: 3,
            optimizer: this.compressContent.bind(this)
        });

        console.log('✓ 优化规则加载完成');
    }

    /**
     * 优化报告
     * @SERVICE 报告优化方法
     * @param {Object} reportContent - 报告内容
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeReport(reportContent) {
        let optimized = JSON.parse(JSON.stringify(reportContent)); // 深拷贝
        const appliedOptimizations = [];

        // 按优先级应用优化规则
        const sortedRules = Array.from(this.optimizationRules.entries())
            .sort((a, b) => a[1].priority - b[1].priority);

        for (const [ruleId, rule] of sortedRules) {
            try {
                const result = await rule.optimizer(optimized);
                if (result.changed) {
                    optimized = result.content;
                    appliedOptimizations.push({
                        rule: rule.name,
                        changes: result.changes || 1
                    });
                }
            } catch (error) {
                console.error(`优化规则 ${rule.name} 执行失败:`, error);
            }
        }

        // 记录优化历史
        this.optimizationHistory.push({
            timestamp: Date.now(),
            originalSize: JSON.stringify(reportContent).length,
            optimizedSize: JSON.stringify(optimized).length,
            appliedOptimizations
        });

        return {
            success: true,
            original: reportContent,
            optimized: optimized,
            appliedOptimizations: appliedOptimizations,
            compressionRatio: this.calculateCompressionRatio(reportContent, optimized)
        };
    }

    /**
     * 去重内容
     * @UTIL 内容去重工具
     * @param {Object} reportContent - 报告内容
     * @returns {Object} 去重结果
     */
    async deduplicateContent(reportContent) {
        // 简化的去重逻辑
        let changed = false;
        const content = { ...reportContent };

        // 这里可以实现具体的去重逻辑
        // 例如：移除重复的统计数据、合并相似的建议等

        return { changed, content };
    }

    /**
     * 优化章节顺序
     * @UTIL 章节顺序优化工具
     * @param {Object} reportContent - 报告内容
     * @returns {Object} 优化结果
     */
    async optimizeSectionOrder(reportContent) {
        const sections = reportContent.sections || {};
        if (Object.keys(sections).length <= 1) {
            return { changed: false, content: reportContent };
        }

        // 定义理想的章节顺序
        const idealOrder = [
            'summary',
            'statistics',
            'categories',
            'service',
            'knowledge',
            'details',
            'recommendations'
        ];

        const orderedSections = {};
        let changed = false;

        // 按理想顺序重新排列章节
        for (const sectionName of idealOrder) {
            if (sections[sectionName]) {
                orderedSections[sectionName] = sections[sectionName];
            }
        }

        // 添加不在理想顺序中的章节
        for (const [sectionName, sectionData] of Object.entries(sections)) {
            if (!idealOrder.includes(sectionName)) {
                orderedSections[sectionName] = sectionData;
            }
        }

        // 检查顺序是否改变
        const originalOrder = Object.keys(sections);
        const newOrder = Object.keys(orderedSections);
        changed = JSON.stringify(originalOrder) !== JSON.stringify(newOrder);

        return {
            changed,
            content: { ...reportContent, sections: orderedSections }
        };
    }

    /**
     * 压缩内容
     * @UTIL 内容压缩工具
     * @param {Object} reportContent - 报告内容
     * @returns {Object} 压缩结果
     */
    async compressContent(reportContent) {
        let changed = false;
        const content = { ...reportContent };

        // 简化的内容压缩逻辑
        // 例如：移除过长的描述、合并相似的数据等

        return { changed, content };
    }

    /**
     * 计算压缩比
     * @UTIL 压缩比计算工具
     * @param {Object} original - 原始内容
     * @param {Object} optimized - 优化后内容
     * @returns {number} 压缩比
     */
    calculateCompressionRatio(original, optimized) {
        const originalSize = JSON.stringify(original).length;
        const optimizedSize = JSON.stringify(optimized).length;
        return originalSize > 0 ? ((originalSize - optimizedSize) / originalSize * 100).toFixed(2) : 0;
    }

    /**
     * 获取优化历史
     * @SERVICE 优化历史获取方法
     * @returns {Array} 优化历史列表
     */
    getOptimizationHistory() {
        return this.optimizationHistory.slice(-20); // 返回最近20次优化
    }

    /**
     * 销毁优化器
     * @LIFECYCLE 优化器销毁方法
     */
    destroy() {
        this.optimizationRules.clear();
        this.optimizationHistory = [];
        this.isInitialized = false;
        console.log('🗑️ ReportOptimizer 已销毁');
    }
}

// ==================== 工具函数 ====================

/**
 * 显示报告进度对话框
 * @SERVICE 报告进度显示函数
 * @returns {HTMLElement} 进度对话框元素
 */
export function showReportProgressDialog() {
    const dialog = document.createElement('div');
    dialog.id = 'report-progress-dialog';
    dialog.className = 'modal-overlay';
    dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    dialog.innerHTML = `
        <div class="modal-content" style="width: 500px; max-width: 90vw;">
            <div class="modal-header">
                <h3><i class="fas fa-file-alt"></i> 报告生成进行中</h3>
            </div>
            <div class="modal-body">
                <div class="report-progress">
                    <div class="progress-info">
                        <div id="report-current-step">初始化报告生成...</div>
                    </div>
                    <div class="progress-bar">
                        <div id="report-progress-fill" class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div class="progress-text">
                        <span id="report-progress-percentage">0%</span>
                    </div>
                </div>
                <div class="modal-actions" style="margin-top: 20px;">
                    <button id="cancel-report-btn" class="btn btn-secondary">取消生成</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);

    // 绑定取消按钮事件
    const cancelBtn = dialog.querySelector('#cancel-report-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            hideReportProgressDialog();
            // 这里可以触发取消事件
            if (window.reportGenerator) {
                window.reportGenerator.cancelGeneration();
            }
        });
    }

    return dialog;
}

/**
 * 更新报告进度
 * @SERVICE 报告进度更新函数
 * @param {Object} progress - 进度信息
 */
export function updateReportProgress(progress) {
    const progressFill = document.getElementById('report-progress-fill');
    const progressPercentage = document.getElementById('report-progress-percentage');
    const currentStep = document.getElementById('report-current-step');

    if (progressFill) {
        progressFill.style.width = `${progress.progress}%`;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${progress.progress}%`;
    }

    if (currentStep) {
        currentStep.textContent = progress.step || '处理中...';
    }
}

/**
 * 隐藏报告进度对话框
 * @SERVICE 报告进度隐藏函数
 */
export function hideReportProgressDialog() {
    const dialog = document.getElementById('report-progress-dialog');
    if (dialog && dialog.parentNode) {
        dialog.parentNode.removeChild(dialog);
    }
}

/**
 * 显示报告预览
 * @SERVICE 报告预览显示函数
 * @param {Object} reportContent - 报告内容
 */
export function showReportPreview(reportContent) {
    const previewSection = document.getElementById('report-preview-section');
    const previewContainer = document.getElementById('report-preview-content');

    if (previewSection) {
        previewSection.style.display = 'block';
    }

    if (previewContainer) {
        const exporter = new ReportExporter();
        previewContainer.innerHTML = exporter.generateHTMLContent(reportContent);
    }

    // 存储当前报告内容
    window.currentReportContent = reportContent;
}

/**
 * 导出当前报告
 * @SERVICE 当前报告导出函数
 * @param {Array} formats - 导出格式列表
 */
export async function exportCurrentReport(formats = ['html']) {
    if (!window.currentReportContent) {
        alert('没有可导出的报告');
        return;
    }

    const exporter = new ReportExporter();

    try {
        const results = await exporter.exportReport(window.currentReportContent, formats);

        let successCount = 0;
        let errorMessages = [];

        Object.entries(results).forEach(([format, result]) => {
            if (result.success) {
                successCount++;
            } else {
                errorMessages.push(`${format}: ${result.error}`);
            }
        });

        if (successCount > 0) {
            alert(`成功导出 ${successCount} 个格式的报告`);
        }

        if (errorMessages.length > 0) {
            console.error('导出错误:', errorMessages);
            alert('部分格式导出失败:\n' + errorMessages.join('\n'));
        }

    } catch (error) {
        console.error('导出报告失败:', error);
        alert('导出报告失败: ' + error.message);
    }
}
