/**
 * ==================== GoMyHire 对话分析系统 - 数据存储管理模块 ====================
 * 
 * @MODULE_INFO
 * 模块名称: App Data Manager System
 * 版本: 2.0.0
 * 功能描述: 应用数据管理系统，管理各类应用数据的存储和检索
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: constants.js (STORAGE_KEYS), 浏览器localStorage API
 * 间接依赖: 无
 * 被依赖: main.js, data-processor.js, ui.js, parser.js
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 1 (第二层，核心数据模块)
 * 加载时机: early (早期加载)
 * 加载条件: 依赖constants.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 应用数据管理器类 (AppDataManager类)
 *   - 数据存储和检索
 *   - 历史数据迁移
 *   - 内存缓存管理
 *   - CSV导出功能
 * 导出接口: AppDataManager, getAppDataManager
 * 全局注册: window.ModuleExports['storage.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 各类应用数据、存储键名
 * 输出数据: 存储的数据、CSV导出文件
 * 状态管理: 维护内存缓存、存储状态
 * 事件处理: 数据变更事件
 * 
 * @INTEGRATION (集成关系)
 * 浏览器集成: 与localStorage深度集成
 * 数据集成: 为数据处理模块提供存储服务
 * UI集成: 为UI组件提供数据支持
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (内存缓存系统)
 * 加载性能: 快速 (轻量级模块)
 * 运行时性能: 高效 (优化的缓存机制)
 */

class AppDataManager {
    constructor() {
        this.storageKeys = window.CONSTANTS?.STORAGE_KEYS || {
            DRIVERS: 'qna_drivers',
            CUSTOMER_SERVICE: 'qna_customer_service',
            SUPPORT_AGENTS: 'qna_support_agents',
            KNOWLEDGE: 'qna_knowledge',
            METRICS: 'qna_metrics',
            QUESTION_CATEGORIES: 'qna_question_categories',
            QUESTION_TAGS: 'qna_question_tags',
            DETAILED_QUESTIONS: 'qna_detailed_questions',
            QA_DATASET: 'qna_qa_dataset',
            SETTINGS: 'qna_settings',
            PROCESSING_STATE: 'qna_processing_state',
            FILE_REGISTRY: 'qna_file_registry',
            ANALYSIS_PROGRESS: 'qna_analysis_progress',
            API_CONFIG: 'qna_api_config',
            REPORT_HISTORY: 'qna_report_history',
            REPORT_TEMPLATES: 'qna_report_templates',
            REPORT_CONFIG: 'qna_report_config'
        };

        this.legacyKeys = {
            DRIVERS: 'enhanced_qna_drivers',
            KNOWLEDGE: 'enhanced_qna_knowledge',
            METRICS: 'enhanced_qna_metrics',
            QUESTION_CATEGORIES: 'enhanced_qna_categories',
            SETTINGS: 'enhanced_qna_settings'
        };

        this.isAvailable = this.checkStorageAvailability();
        this.tryMigrateLegacyKeys();

        // 内存管理
        this.memoryCache = new Map();
        this.maxMemoryCacheSize = 50;
        this.compressionThreshold = 10000; // 10KB
    }

    /**
     * 检查localStorage可用性
     */
    checkStorageAvailability() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 从增强版localStorage键名迁移到标准qna_*键名
     */
    tryMigrateLegacyKeys() {
        if (!this.isAvailable) return;

        const MIGRATION_FLAG = 'qna_migrated_v2';

        try {
            // 检查是否已经迁移过
            if (localStorage.getItem(MIGRATION_FLAG)) {
                return;
            }

            const migrateIfNeeded = (oldKey, newKey) => {
                const oldData = localStorage.getItem(oldKey);
                if (oldData && !localStorage.getItem(newKey)) {
                    localStorage.setItem(newKey, oldData);
                    localStorage.removeItem(oldKey);
                }
            };

            migrateIfNeeded(this.legacyKeys.DRIVERS, this.storageKeys.DRIVERS);
            migrateIfNeeded(this.legacyKeys.KNOWLEDGE, this.storageKeys.KNOWLEDGE);
            migrateIfNeeded(this.legacyKeys.METRICS, this.storageKeys.METRICS);
            migrateIfNeeded(this.legacyKeys.QUESTION_CATEGORIES, this.storageKeys.QUESTION_CATEGORIES);
            migrateIfNeeded(this.legacyKeys.SETTINGS, this.storageKeys.SETTINGS);

            localStorage.removeItem('qna_migrated_v1');
            localStorage.setItem(MIGRATION_FLAG, 'true');

        } catch (e) {
            console.warn('[Storage] Migration failed (ignored):', e);
        }
    }

    /**
     * 保存数据到localStorage
     */
    save(key, data) {
        if (!this.isAvailable) return false;

        try {
            let jsonData = JSON.stringify(data);

            // 大数据压缩（简单的字符串压缩）
            if (jsonData.length > this.compressionThreshold) {
                jsonData = this.compressString(jsonData);
                key = `compressed_${key}`;
            }

            localStorage.setItem(key, jsonData);

            // 更新内存缓存
            this.updateMemoryCache(key, data);

            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 从localStorage加载数据
     */
    load(key, defaultValue = null) {
        if (!this.isAvailable) return defaultValue;

        // 检查内存缓存
        if (this.memoryCache.has(key)) {
            return this.memoryCache.get(key);
        }

        try {
            let jsonData = localStorage.getItem(key);

            // 尝试压缩版本
            if (!jsonData) {
                jsonData = localStorage.getItem(`compressed_${key}`);
                if (jsonData) {
                    jsonData = this.decompressString(jsonData);
                }
            }

            if (!jsonData) return defaultValue;

            const data = JSON.parse(jsonData);

            // 更新内存缓存
            this.updateMemoryCache(key, data);

            return data;
        } catch (e) {
            return defaultValue;
        }
    }

    remove(key) {
        if (!this.isAvailable) return false;

        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 清空所有应用数据
     */
    clearAll() {
        if (!this.isAvailable) return false;

        try {
            Object.values(this.storageKeys).forEach(key => {
                localStorage.removeItem(key);
            });
            Object.values(this.legacyKeys).forEach(oldKey => {
                localStorage.removeItem(oldKey);
            });
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 保存司机数据
     */
    saveDriverData(data) {
        return this.save(this.storageKeys.DRIVERS, data);
    }

    /**
     * 加载司机数据
     */
    loadDriverData() {
        return this.load(this.storageKeys.DRIVERS, []);
    }

    /**
     * 保存客服数据
     */
    saveCustomerServiceData(data) {
        return this.save(this.storageKeys.CUSTOMER_SERVICE, data);
    }

    /**
     * 加载客服数据
     */
    loadCustomerServiceData() {
        return this.load(this.storageKeys.CUSTOMER_SERVICE, []);
    }

    /**
     * 保存知识库数据
     */
    saveKnowledgeData(data) {
        return this.save(this.storageKeys.KNOWLEDGE, data);
    }

    /**
     * 加载知识库数据
     */
    loadKnowledgeData() {
        return this.load(this.storageKeys.KNOWLEDGE, []);
    }

    /**
     * 保存问答数据集
     */
    saveQADataset(data) {
        return this.save(this.storageKeys.QA_DATASET, data);
    }

    /**
     * 加载问答数据集
     */
    loadQADataset() {
        return this.load(this.storageKeys.QA_DATASET, []);
    }

    /**
     * 保存分析结果
     */
    saveAnalysisResults(data) {
        const timestamp = Date.now();
        const results = this.load(this.storageKeys.DETAILED_QUESTIONS, []);

        // 添加时间戳
        const dataWithTimestamp = {
            ...data,
            timestamp,
            id: `analysis_${timestamp}`
        };

        results.push(dataWithTimestamp);
        return this.save(this.storageKeys.DETAILED_QUESTIONS, results);
    }

    /**
     * 加载分析结果
     */
    loadAnalysisResults() {
        return this.load(this.storageKeys.DETAILED_QUESTIONS, []);
    }

    /**
     * 获取存储使用情况
     */
    getStorageInfo() {
        if (!this.isAvailable) {
            return { available: false };
        }

        let totalSize = 0;
        const itemSizes = {};

        Object.entries(this.storageKeys).forEach(([name, key]) => {
            const data = localStorage.getItem(key);
            const size = data ? new Blob([data]).size : 0;
            itemSizes[name] = size;
            totalSize += size;
        });

        return {
            available: true,
            totalSize,
            itemSizes,
            totalSizeFormatted: this.formatBytes(totalSize)
        };
    }

    /**
     * 格式化字节大小
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 导出数据为CSV格式
     */
    exportToCSV(dataType, customData = null) {
        try {
            let data, filename, headers;

            switch (dataType) {
                case 'drivers':
                    data = customData || this.loadDriverData();
                    filename = 'drivers_data.csv';
                    headers = ['司机姓名', '总对话数', '平均满意度', '平均有效性', '最后更新时间'];
                    break;

                case 'analysis_results':
                    data = customData || this.loadAnalysisResults();
                    filename = 'analysis_results.csv';
                    headers = ['文件名', '对话数', '消息数', '分析时间', '有效性', '满意度', '问题分类'];
                    break;

                default:
                    throw new Error(`不支持的数据类型: ${dataType}`);
            }

            if (!data || data.length === 0) {
                console.warn(`[Storage] No data to export for type: ${dataType}`);
                return null;
            }

            // 检查Papa Parse是否可用
            if (typeof Papa === 'undefined') {
                console.warn('[Storage] Papa Parse not available, using fallback CSV export');
                return this.exportToCSVFallback(data, headers, filename);
            }

            // 使用Papa Parse导出
            const csv = Papa.unparse({
                fields: headers,
                data: this.formatDataForCSV(data, dataType)
            });

            // 添加UTF-8 BOM以支持Excel
            const csvWithBOM = '\ufeff' + csv;

            // 创建下载
            this.downloadCSV(csvWithBOM, filename);

            console.log(`[Storage] CSV exported successfully: ${filename}`);
            return csvWithBOM;

        } catch (error) {
            console.error(`[Storage] CSV export failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * 格式化数据用于CSV导出
     */
    formatDataForCSV(data, dataType) {
        switch (dataType) {
            case 'drivers':
                return data.map(item => [
                    item.name || '',
                    item.totalConversations || 0,
                    item.avgSatisfaction || 0,
                    item.avgEffectiveness || 0,
                    new Date(item.lastUpdated || 0).toLocaleString()
                ]);

            case 'analysis_results':
                return data.map(item => [
                    item.fileName || '',
                    item.stats?.totalConversations || 0,
                    item.stats?.totalMessages || 0,
                    new Date(item.timestamp || 0).toLocaleString(),
                    item.analysis?.effectiveness || 0,
                    item.analysis?.satisfaction || 0,
                    item.analysis?.question_category || ''
                ]);

            default:
                return data;
        }
    }

    /**
     * 下载CSV文件
     */
    downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }

    /**
     * 简单字符串压缩（RLE算法）
     */
    compressString(str) {
        return str.replace(/(.)\1+/g, (match, char) => {
            return match.length > 3 ? `${char}${match.length}` : match;
        });
    }

    /**
     * 简单字符串解压缩
     */
    decompressString(str) {
        return str.replace(/(.)\d+/g, (match, char) => {
            const count = parseInt(match.slice(1));
            return char.repeat(count);
        });
    }

    /**
     * 更新内存缓存
     */
    updateMemoryCache(key, data) {
        if (this.memoryCache.size >= this.maxMemoryCacheSize) {
            const firstKey = this.memoryCache.keys().next().value;
            this.memoryCache.delete(firstKey);
        }
        this.memoryCache.set(key, data);
    }

    /**
     * 清理内存缓存
     */
    clearMemoryCache() {
        this.memoryCache.clear();
    }

    /**
     * 获取内存使用统计
     */
    getMemoryStats() {
        let totalSize = 0;
        for (const [key, value] of this.memoryCache) {
            totalSize += JSON.stringify(value).length;
        }

        return {
            cacheSize: this.memoryCache.size,
            maxCacheSize: this.maxMemoryCacheSize,
            totalMemoryBytes: totalSize,
            totalMemoryMB: (totalSize / 1024 / 1024).toFixed(2)
        };
    }
}

// ==================== 全局应用数据管理器实例 ====================
let appDataManagerInstance = null;

/**
 * 获取全局应用数据管理器实例
 * @SERVICE 全局应用数据管理器获取函数
 * @returns {AppDataManager} 应用数据管理器实例
 */
function getAppDataManager() {
    if (!appDataManagerInstance) {
        appDataManagerInstance = new AppDataManager();
    }
    return appDataManagerInstance;
}

// 创建默认全局实例
const appDataManager = getAppDataManager();

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['storage.js'] = {
    AppDataManager,
    getAppDataManager,
    appDataManager // 兼容性导出
};
