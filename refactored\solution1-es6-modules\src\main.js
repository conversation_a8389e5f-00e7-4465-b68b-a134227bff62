/**
 * ==================== GoMyHire 对话分析系统 - 主应用模块 ====================
 * @SERVICE 完整重构的主应用模块，集成所有新创建的模块化组件
 * 实现系统初始化、模块管理、事件协调、状态管理等核心功能
 */

// ==================== 模块引用（使用全局变量）====================
// 获取全局模块引用
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// 其他模块引用将在需要时通过 window.ModuleExports 访问

// ==================== 任务池类（保留兼容性）====================
/**
 * 任务池类 - 用于批量处理任务的并发控制
 * @COMPONENT 任务池管理器
 */
class TaskPool {
    constructor(maxConcurrency = 50) {
        this.maxConcurrency = maxConcurrency;
        this.running = new Set();
        this.queue = [];
        this.completed = [];
        this.failed = [];
    }

    /**
     * 添加任务到队列
     * @SERVICE 任务添加方法
     */
    async addTask(taskFn, taskData = null) {
        return new Promise((resolve, reject) => {
            const task = {
                id: getUtils()?.generateUniqueId('task') || 'task_' + Date.now(),
                taskFn,
                taskData,
                resolve,
                reject,
                startTime: null,
                endTime: null
            };

            this.queue.push(task);
            this.processQueue();
        });
    }

    /**
     * 处理任务队列
     * @SERVICE 任务队列处理方法
     */
    processQueue() {
        while (this.running.size < this.maxConcurrency && this.queue.length > 0) {
            const task = this.queue.shift();
            this.runTask(task);
        }
    }

    /**
     * 执行单个任务
     * @SERVICE 单个任务执行方法
     */
    async runTask(task) {
        this.running.add(task);
        task.startTime = Date.now();

        try {
            const result = await (getUtils()?.safeExecute(() => task.taskFn(task.taskData)) || task.taskFn(task.taskData));

            task.endTime = Date.now();
            task.result = result;
            this.completed.push(task);

            task.resolve(result);

        } catch (error) {
            task.endTime = Date.now();
            task.error = error;
            this.failed.push(task);

            task.reject(error);

        } finally {
            this.running.delete(task);
            this.processQueue();
        }
    }

    /**
     * 等待所有任务完成
     * @SERVICE 任务完成等待方法
     */
    async waitAll() {
        while (this.running.size > 0 || this.queue.length > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        return {
            completed: this.completed.length,
            failed: this.failed.length,
            total: this.completed.length + this.failed.length
        };
    }

    /**
     * 获取任务统计
     * @SERVICE 任务统计获取方法
     */
    getStats() {
        return {
            running: this.running.size,
            queued: this.queue.length,
            completed: this.completed.length,
            failed: this.failed.length,
            maxConcurrency: this.maxConcurrency
        };
    }

    /**
     * 清空任务池
     * @SERVICE 任务池清空方法
     */
    clear() {
        this.queue = [];
        this.completed = [];
        this.failed = [];
    }
}

// ==================== 主应用类 ====================
/**
 * 主应用类 - 负责整个应用的初始化、管理和协调
 * @COMPONENT 主应用管理器
 */
class MainApp {
    constructor() {
        this.isInitialized = false;
        this.isProcessing = false;
        this.appState = {
            currentTab: 'analysis',
            totalFiles: 0,
            processedFiles: 0,
            failedFiles: 0,
            isProcessing: false,
            lastUpdate: null
        };

        // 服务管理器
        this.serviceContainer = null;
        this.eventBus = null;
        this.dependencyManager = null;

        // 数据处理系统
        this.storageManager = null;
        this.dataProcessor = null;
        this.performanceMonitor = null;

        // UI组件系统
        this.notificationManager = null;
        this.modalManager = null;
        this.tabManager = null;
        this.progressManager = null;

        // 高级功能系统
        this.uploadManager = null;
        this.qaOptimizer = null;
        this.reportGenerator = null;
        this.tagCenter = null;

        // 原有模块兼容
        this.modules = {};
        this.uiManager = null;
        this.taskPool = null;

        console.log('🚀 MainApp 实例创建完成');
    }

    /**
     * 初始化应用
     * @INIT 应用初始化方法
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async initialize() {
        if (this.isInitialized) {
            console.warn('应用已经初始化');
            return true;
        }

        try {
            console.log('🔄 开始初始化主应用...');

            // 1. 初始化基础服务
            await this.initializeBaseServices();

            // 2. 初始化数据处理系统
            await this.initializeDataSystems();

            // 3. 初始化UI组件系统
            await this.initializeUIComponents();

            // 4. 初始化高级功能系统
            await this.initializeAdvancedFeatures();

            // 5. 初始化原有模块兼容
            await this.initializeLegacyModules();

            // 6. 设置事件监听器
            this.setupEventListeners();

            // 7. 初始化UI界面
            this.initializeUI();

            // 8. 加载历史数据
            await this.loadHistoryData();

            this.isInitialized = true;
            console.log('✅ 主应用初始化完成');

            // 显示成功消息
            this.showSuccessMessage();

            return true;

        } catch (error) {
            console.error('❌ 主应用初始化失败:', error);
            this.showErrorMessage(error.message);
            return false;
        }
    }

    /**
     * 初始化基础服务
     * @INIT 基础服务初始化方法
     */
    async initializeBaseServices() {
        console.log('🔧 初始化基础服务...');

        // 服务容器
        const serviceContainerModule = window.ModuleExports && window.ModuleExports['service-container.js'];
        this.serviceContainer = serviceContainerModule ? serviceContainerModule.getServiceContainer() : null;
        
        // 事件总线
        const eventBusModule = window.ModuleExports && window.ModuleExports['event-bus.js'];
        this.eventBus = eventBusModule ? eventBusModule.getEventBus() : null;
        
        // 依赖管理器
        const dependencyModule = window.ModuleExports && window.ModuleExports['dependency-manager.js'];
        this.dependencyManager = dependencyModule ? dependencyModule.getDependencyManager() : null;

        // 注册核心服务
        this.serviceContainer.register('eventBus', this.eventBus);
        this.serviceContainer.register('dependencyManager', this.dependencyManager);

        console.log('✅ 基础服务初始化完成');
    }

    /**
     * 初始化数据处理系统
     * @INIT 数据处理系统初始化方法
     */
    async initializeDataSystems() {
        console.log('🗄️ 初始化数据处理系统...');

        // 存储管理器
        const storageModule = window.ModuleExports && window.ModuleExports['storage-manager.js'];
        this.storageManager = storageModule ? storageModule.getStorageManager() : null;
        
        // 数据处理器
        const dataProcessorModule = window.ModuleExports && window.ModuleExports['data-processor.js'];
        this.dataProcessor = dataProcessorModule ? dataProcessorModule.getDataProcessor(this.storageManager, this.eventBus) : null;
        
        // 性能监控器
        const performanceModule = window.ModuleExports && window.ModuleExports['performance-monitor.js'];
        this.performanceMonitor = performanceModule ? performanceModule.getPerformanceMonitor() : null;

        // 注册到服务容器
        this.serviceContainer.register('storageManager', this.storageManager);
        this.serviceContainer.register('dataProcessor', this.dataProcessor);
        this.serviceContainer.register('performanceMonitor', this.performanceMonitor);

        console.log('✅ 数据处理系统初始化完成');
    }

    /**
     * 初始化UI组件系统
     * @INIT UI组件系统初始化方法
     */
    async initializeUIComponents() {
        console.log('🎨 初始化UI组件系统...');

        // 通知管理器
        const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
        this.notificationManager = notificationModule ? notificationModule.getNotificationManager() : null;
        
        // 模态框管理器
        const modalModule = window.ModuleExports && window.ModuleExports['modal.js'];
        this.modalManager = modalModule ? modalModule.getModalManager() : null;
        
        // 进度管理器
        const progressModule = window.ModuleExports && window.ModuleExports['progress.js'];
        this.progressManager = progressModule ? progressModule.getProgressManager() : null;

        // 标签页系统
        const tabModule = window.ModuleExports && window.ModuleExports['tabs.js'];
        if (tabModule && tabModule.initializeTabSystem) {
            const tabSystemResult = tabModule.initializeTabSystem({
                container: '.tabs-container',
                eventBus: this.eventBus
            });
            this.tabManager = tabSystemResult.tabManager;
        } else if (tabModule && tabModule.getTabManager) {
            this.tabManager = tabModule.getTabManager();
        } else {
            console.warn('[Main] 标签页模块未找到');
            this.tabManager = null;
        }

        // 注册到服务容器
        this.serviceContainer.register('notificationManager', this.notificationManager);
        this.serviceContainer.register('modalManager', this.modalManager);
        this.serviceContainer.register('tabManager', this.tabManager);
        this.serviceContainer.register('progressManager', this.progressManager);

        console.log('✅ UI组件系统初始化完成');
    }

    /**
     * 初始化高级功能系统
     * @INIT 高级功能系统初始化方法
     */
    async initializeAdvancedFeatures() {
        console.log('⚡ 初始化高级功能系统...');

        // 增强上传管理器
        const uploadModule = window.ModuleExports && window.ModuleExports['enhanced-upload.js'];
        this.uploadManager = uploadModule ? uploadModule.getEnhancedUploadManager(this.storageManager, this.eventBus) : null;
        
        // QA优化管理器
        const qaModule = window.ModuleExports && window.ModuleExports['qa-optimization.js'];
        this.qaOptimizer = qaModule ? qaModule.getQAOptimizationManager(this.dataProcessor, this.eventBus) : null;
        
        // 报告生成器
        const reportModule = window.ModuleExports && window.ModuleExports['report-generator.js'];
        this.reportGenerator = reportModule ? reportModule.getReportGenerator(this.dataProcessor, this.eventBus) : null;
        
        // 标签中心
        const tagModule = window.ModuleExports && window.ModuleExports['tag-center.js'];
        this.tagCenter = tagModule ? tagModule.getTagCenter() : null;

        // 注册到服务容器
        this.serviceContainer.register('uploadManager', this.uploadManager);
        this.serviceContainer.register('qaOptimizer', this.qaOptimizer);
        this.serviceContainer.register('reportGenerator', this.reportGenerator);
        this.serviceContainer.register('tagCenter', this.tagCenter);

        console.log('✅ 高级功能系统初始化完成');
    }

    /**
     * 初始化原有模块兼容
     * @INIT 原有模块兼容初始化方法
     */
     async initializeLegacyModules() {
        console.log('🔄 初始化原有模块兼容...');

        // 检查依赖
        this.checkDependencies();

        // 加载模块引用
        this.loadModuleReferences();

        // 初始化任务池
        this.initializeTaskPool();

        console.log('✅ 原有模块兼容初始化完成');
    }

    /**
     * 检查模块依赖
     * @SERVICE 模块依赖检查方法
     */
    checkDependencies() {
        const requiredModules = ['storage.js', 'parser.js', 'charts.js', 'drag-upload.js'];
        const missing = [];

        requiredModules.forEach(moduleName => {
            if (!window.ModuleExports || !window.ModuleExports[moduleName]) {
                missing.push(moduleName);
            }
        });

        if (missing.length > 0) {
            throw new Error(`缺少必需的模块: ${missing.join(', ')}`);
        }

        console.log('[Main] 所有依赖检查完成');
    }

    /**
     * 加载模块引用
     * @SERVICE 模块引用加载方法
     */
    loadModuleReferences() {
        this.modules = {
            storage: window.ModuleExports['storage.js'],
            parser: window.ModuleExports['parser.js'],
            charts: window.ModuleExports['charts.js'],
            dragUpload: window.ModuleExports['drag-upload.js']
        };

        console.log('[Main] 模块引用加载完成');
    }

    /**
     * 初始化任务池
     * @SERVICE 任务池初始化方法
     */
    initializeTaskPool() {
        const maxConcurrency = window.LOCAL_CONFIG?.maxConcurrency ||
                              window.CONSTANTS?.MAX_CONCURRENCY || 50;

        this.taskPool = new TaskPool(maxConcurrency);
    }

    /**
     * 初始化UI界面
     * @SERVICE UI界面初始化方法
     */
    initializeUI() {
        // 隐藏加载界面
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // 显示主应用界面
        const mainAppElement = document.getElementById('main-app');
        if (mainAppElement) {
            mainAppElement.style.display = 'block';
        }

        // 初始化UI管理器
        if (window.ModuleExports && window.ModuleExports['ui.js']) {
            this.uiManager = window.ModuleExports['ui.js'];
        }

        console.log('[Main] UI界面初始化完成');
    }

    /**
     * 设置事件监听器
     * @SERVICE 事件监听器设置方法
     */
    setupEventListeners() {
        // 文件上传相关事件
        this.setupFileUploadListeners();

        // UI按钮事件
        this.setupUIButtonListeners();

        // 标签页切换事件
        this.setupTabChangeListeners();

        // 系统事件
        this.setupSystemEventListeners();

        console.log('[Main] 事件监听器设置完成');
    }

    /**
     * 设置文件上传监听器
     * @SERVICE 文件上传监听器设置方法
     */
    setupFileUploadListeners() {
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');

        if (dropZone && fileInput) {
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    this.processBatchFiles(files);
                }
            });
        }
    }

    /**
     * 设置UI按钮监听器
     * @SERVICE UI按钮监听器设置方法
     */
    setupUIButtonListeners() {
        // 开始分析按钮
        const startAnalysisBtn = document.getElementById('start-analysis-btn');
        if (startAnalysisBtn) {
            startAnalysisBtn.addEventListener('click', () => {
                this.startAnalysis();
            });
        }

        // 导出结果按钮
        const exportResultsBtn = document.getElementById('export-results-btn');
        if (exportResultsBtn) {
            exportResultsBtn.addEventListener('click', () => {
                this.exportResults();
            });
        }

        // 清空数据按钮
        const clearDataBtn = document.getElementById('clear-data-btn');
        if (clearDataBtn) {
            clearDataBtn.addEventListener('click', () => {
                this.clearAllData();
            });
        }

        // 保存设置按钮
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }
    }

    /**
     * 设置标签页切换监听器
     * @SERVICE 标签页切换监听器设置方法
     */
    setupTabChangeListeners() {
        // 监听标签页切换事件
        document.addEventListener('tab.switched', (e) => {
            this.onTabChanged(e.detail.currentTab);
        });

        // 监听标签页数据更新事件
        document.addEventListener('tab.data.updated', (e) => {
            this.onTabDataUpdated(e.detail);
        });
    }

    /**
     * 设置系统事件监听器
     * @SERVICE 系统事件监听器设置方法
     */
    setupSystemEventListeners() {
        // 监听操作取消事件
        document.addEventListener('operationCancelled', () => {
            this.cancelCurrentOperation();
        });

        // 监听文件处理完成事件
        document.addEventListener('fileProcessed', (e) => {
            this.onFileProcessed(e.detail);
        });

        // 监听错误事件
        document.addEventListener('systemError', (e) => {
            this.onSystemError(e.detail);
        });
    }

    /**
     * 批量处理文件
     * @SERVICE 批量文件处理方法
     * @param {Array} files - 文件列表
     */
    async processBatchFiles(files) {
        if (!files || files.length === 0) {
            console.warn('[Main] 没有文件需要处理');
            return;
        }

        try {
            this.appState.isProcessing = true;
            this.appState.totalFiles = files.length;
            this.appState.processedFiles = 0;
            this.appState.failedFiles = 0;

            console.log(`[Main] 开始批量处理 ${files.length} 个文件`);

            // 显示处理状态
            showProcessingStatus();

            // 使用增强上传管理器处理文件
            if (this.uploadManager) {
                const results = await this.uploadManager.processBatchFiles(files);
                this.handleBatchProcessingResults(results);
            } else {
                // 回退到原有处理方式
                await this.processBatchFilesLegacy(files);
            }

        } catch (error) {
            console.error('[Main] 批量处理失败:', error);
            this.showErrorMessage(`批量处理失败: ${error.message}`);
        } finally {
            this.appState.isProcessing = false;
        }
    }

    /**
     * 处理批量处理结果
     * @SERVICE 批量处理结果处理方法
     * @param {Array} results - 处理结果
     */
    handleBatchProcessingResults(results) {
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        console.log(`[Main] 批量处理完成: ${successful} 成功, ${failed} 失败`);

        // 更新图表和UI
        this.updateChartsWithResults();
        this.showProcessingComplete(successful, failed);
    }

    /**
     * 原有批量处理方式（兼容性）
     * @SERVICE 原有批量处理方法
     * @param {Array} files - 文件列表
     */
    async processBatchFilesLegacy(files) {
        // 为每个文件创建处理任务
        const tasks = files.map(file => () => this.processFileWithAI(file));

        // 使用任务池并发处理
        const results = await Promise.allSettled(
            tasks.map(task => this.taskPool.addTask(task))
        );

        this.handleBatchProcessingResults(results);
    }

    /**
     * 显示成功消息
     * @SERVICE 成功消息显示方法
     */
    showSuccessMessage() {
        console.log('[Main] 🎉 模块化系统测试成功！所有模块正常加载。');
        const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
        if (notificationModule && notificationModule.showSuccess) {
            notificationModule.showSuccess('系统初始化完成！', {
                title: '初始化成功',
                duration: 5000
            });
        } else {
            console.log('✅ 系统初始化完成！');
        }
    }

    /**
     * 显示错误消息
     * @SERVICE 错误消息显示方法
     * @param {string} message - 错误消息
     */
    showErrorMessage(message) {
        console.error('[Main] 错误:', message);
        // 尝试使用通知模块显示错误
        const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
        if (notificationModule && notificationModule.showError) {
            notificationModule.showError(`系统错误: ${message}`, {
                title: '系统错误',
                duration: 8000
            });
        } else {
            // Fallback到简单的console输出
            console.error('系统错误:', message);
        }
    }

    /**
     * 加载历史数据
     * @SERVICE 历史数据加载方法
     */
    async loadHistoryData() {
        try {
            if (this.storageManager) {
                const historyData = await this.storageManager.loadAnalysisResults();
                if (historyData.length > 0) {
                    console.log(`[Main] 加载了 ${historyData.length} 条历史分析结果`);
                    this.updateChartsWithResults();
                }
            }
        } catch (error) {
            console.warn('[Main] 加载历史数据失败:', error);
        }
    }

    // ==================== 事件处理方法 ====================

    /**
     * 标签页切换事件处理
     * @EVENT_HANDLER 标签页切换事件处理器
     * @param {string} tabName - 标签页名称
     */
    onTabChanged(tabName) {
        console.log(`[Main] 切换到标签页: ${tabName}`);
        this.appState.currentTab = tabName;

        // 根据标签页执行相应操作
        switch (tabName) {
            case 'analysis':
                this.updateChartsWithResults();
                break;
            case 'reports':
                this.refreshReportsData();
                break;
            case 'qa-dataset':
                this.refreshQADataset();
                break;
        }
    }

    /**
     * 标签页数据更新事件处理
     * @EVENT_HANDLER 标签页数据更新事件处理器
     * @param {Object} detail - 事件详情
     */
    onTabDataUpdated(detail) {
        console.log(`[Main] 标签页数据更新: ${detail.tabId}`);
        // 可以在这里添加数据更新后的处理逻辑
    }

    /**
     * 文件处理完成事件处理
     * @EVENT_HANDLER 文件处理完成事件处理器
     * @param {Object} detail - 事件详情
     */
    onFileProcessed(detail) {
        this.appState.processedFiles++;
        this.appState.lastUpdate = Date.now();

        // 更新进度显示
        this.updateProgressUI();
    }

    /**
     * 系统错误事件处理
     * @EVENT_HANDLER 系统错误事件处理器
     * @param {Object} detail - 错误详情
     */
    onSystemError(detail) {
        console.error('[Main] 系统错误:', detail);
        this.showErrorMessage(detail.message || '系统发生未知错误');
    }

    // ==================== 业务方法 ====================

    /**
     * 开始分析
     * @SERVICE 分析开始方法
     */
    async startAnalysis() {
        const files = this.getSelectedFiles();
        if (files.length === 0) {
            const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
            if (notificationModule && notificationModule.showInfo) {
                notificationModule.showInfo('请先选择要分析的文件');
            } else {
                console.log('ℹ️ 请先选择要分析的文件');
            }
            return;
        }

        await this.processBatchFiles(files);
    }

    /**
     * 导出结果
     * @SERVICE 结果导出方法
     */
    async exportResults() {
        try {
            if (this.reportGenerator) {
                const report = await this.reportGenerator.generateComprehensiveReport();
                const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
                if (notificationModule && notificationModule.showSuccess) {
                    notificationModule.showSuccess('报告导出成功！');
                } else {
                    console.log('✅ 报告导出成功！');
                }
            } else {
                // 回退到原有导出方式
                this.exportResultsLegacy();
            }
        } catch (error) {
            this.showErrorMessage(`导出失败: ${error.message}`);
        }
    }

    /**
     * 清空所有数据
     * @SERVICE 数据清空方法
     */
    async clearAllData() {
        const confirmed = await showConfirm(
            '确认清空',
            '确定要清空所有数据吗？此操作不可撤销。',
            () => true,
            () => false
        );

        if (confirmed) {
            if (this.storageManager) {
                await this.storageManager.clearAll();
            }
            const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
            if (notificationModule && notificationModule.showSuccess) {
                notificationModule.showSuccess('数据已清空');
            } else {
                console.log('✅ 数据已清空');
            }
        }
    }

    /**
     * 保存设置
     * @SERVICE 设置保存方法
     */
    saveSettings() {
        const apiKeyInput = document.getElementById('api-key-input');
        const maxConcurrencyInput = document.getElementById('max-concurrency-input');

        if (apiKeyInput && maxConcurrencyInput) {
            // 保存到localStorage
            localStorage.setItem('apiKey', apiKeyInput.value);
            localStorage.setItem('maxConcurrency', maxConcurrencyInput.value);

            // 更新全局配置
            if (!window.LOCAL_CONFIG) window.LOCAL_CONFIG = {};
            window.LOCAL_CONFIG.apiKey = apiKeyInput.value;
            window.LOCAL_CONFIG.maxConcurrency = parseInt(maxConcurrencyInput.value);

            const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
            if (notificationModule && notificationModule.showSuccess) {
                notificationModule.showSuccess('设置已保存');
            } else {
                console.log('✅ 设置已保存');
            }
        }
    }

    /**
     * 获取选中的文件
     * @SERVICE 选中文件获取方法
     * @returns {Array} 文件列表
     */
    getSelectedFiles() {
        const fileInput = document.getElementById('file-input');
        return fileInput ? Array.from(fileInput.files) : [];
    }

    /**
     * 取消当前操作
     * @SERVICE 当前操作取消方法
     */
    cancelCurrentOperation() {
        if (this.taskPool) {
            this.taskPool.clear();
        }
        this.appState.isProcessing = false;
        console.log('[Main] 操作已取消');
    }

    /**
     * 更新进度UI
     * @SERVICE 进度UI更新方法
     */
    updateProgressUI() {
        const progress = this.appState.totalFiles > 0 ?
            Math.round((this.appState.processedFiles / this.appState.totalFiles) * 100) : 0;

        // 更新进度显示
        const progressElement = document.getElementById('processing-progress');
        if (progressElement) {
            progressElement.textContent = `${progress}%`;
        }

        const statusElement = document.getElementById('processing-status');
        if (statusElement) {
            statusElement.textContent =
                `已处理: ${this.appState.processedFiles}/${this.appState.totalFiles} (失败: ${this.appState.failedFiles})`;
        }
    }

    /**
     * 更新图表显示
     * @SERVICE 图表显示更新方法
     */
    updateChartsWithResults() {
        try {
            if (!this.modules.charts) return;

            const allResults = this.modules.storage?.loadAnalysisResults() || [];
            if (allResults.length === 0) {
                console.warn('[Main] 没有分析结果可显示');
                return;
            }

            // 聚合数据用于图表显示
            const aggregatedData = this.aggregateAnalysisData(allResults);

            // 更新所有图表
            this.modules.charts.updateAllCharts(aggregatedData);

            console.log('[Main] 图表已更新');

        } catch (error) {
            console.error('[Main] 图表更新失败:', error);
        }
    }

    /**
     * 聚合分析数据
     * @UTIL 分析数据聚合工具
     * @param {Array} results - 分析结果
     * @returns {Object} 聚合数据
     */
    aggregateAnalysisData(results) {
        const aggregated = {
            totalConversations: 0,
            avgSatisfaction: 0,
            avgEffectiveness: 0,
            questionCategories: {},
            wordFrequency: {},
            timeDistribution: {}
        };

        results.forEach(result => {
            if (result.conversations) {
                aggregated.totalConversations += result.conversations.length;

                result.conversations.forEach(conv => {
                    // 聚合满意度
                    if (conv.satisfaction) {
                        aggregated.avgSatisfaction += conv.satisfaction;
                    }

                    // 聚合有效性
                    if (conv.effectiveness) {
                        aggregated.avgEffectiveness += conv.effectiveness;
                    }

                    // 聚合问题分类
                    if (conv.category) {
                        aggregated.questionCategories[conv.category] =
                            (aggregated.questionCategories[conv.category] || 0) + 1;
                    }
                });
            }
        });

        // 计算平均值
        if (aggregated.totalConversations > 0) {
            aggregated.avgSatisfaction /= aggregated.totalConversations;
            aggregated.avgEffectiveness /= aggregated.totalConversations;
        }

        return aggregated;
    }

    /**
     * 显示处理完成消息
     * @SERVICE 处理完成消息显示方法
     * @param {number} successful - 成功数量
     * @param {number} failed - 失败数量
     */
    showProcessingComplete(successful, failed) {
        const message = `处理完成！成功: ${successful}, 失败: ${failed}`;
        console.log(`[Main] ${message}`);
        const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
        if (notificationModule && notificationModule.showSuccess) {
            notificationModule.showSuccess(message);
        } else {
            console.log('✅ ' + message);
        }
    }

    /**
     * 刷新报告数据
     * @SERVICE 报告数据刷新方法
     */
    refreshReportsData() {
        if (this.reportGenerator) {
            // 使用新的报告生成器
            this.reportGenerator.refreshData();
        }
    }

    /**
     * 刷新QA数据集
     * @SERVICE QA数据集刷新方法
     */
    refreshQADataset() {
        if (this.qaOptimizer) {
            // 使用新的QA优化器
            this.qaOptimizer.refreshDataset();
        }
    }

    // ==================== 兼容性方法 ====================

    /**
     * 原有导出方式（兼容性）
     * @SERVICE 原有导出方法
     */
    exportResultsLegacy() {
        if (!this.modules.storage) return;

        try {
            const results = this.modules.storage.loadAnalysisResults();
            if (results.length === 0) {
                showInfo('没有可导出的分析结果');
                return;
            }

            this.modules.storage.exportToCSV(results, 'analysis_results');
            const notificationModule = window.ModuleExports && window.ModuleExports['notification.js'];
            if (notificationModule && notificationModule.showSuccess) {
                notificationModule.showSuccess('导出成功！');
            } else {
                console.log('✅ 导出成功！');
            }
        } catch (error) {
            this.showErrorMessage(`导出失败: ${error.message}`);
        }
    }

    /**
     * 使用AI处理文件（兼容性）
     * @SERVICE AI文件处理方法
     * @param {File} file - 文件对象
     * @returns {Promise} 处理结果
     */
    async processFileWithAI(file) {
        try {
            this.appState.processedFiles++;

            console.log(`[Main] 处理文件: ${file.name}`);

            const fileData = await this.modules.dragUpload.readFile(file);
            const parsedData = this.modules.parser.parseTxtContent(fileData.content, file.name);

            // 进行AI分析（如果配置了API密钥）
            const apiKey = window.LOCAL_CONFIG?.apiKey;
            if (apiKey && apiKey !== 'sk-your-kimi-api-key-here') {
                for (const conversation of parsedData.conversations) {
                    const analysis = await this.modules.parser.evaluateConversationWithKimi(
                        conversation,
                        apiKey
                    );
                    Object.assign(conversation, analysis);
                }
            }

            // 保存处理结果
            this.modules.storage.save(`file_${Date.now()}`, parsedData);

            // 更新进度UI
            this.updateProgressUI();

            console.log(`[Main] 文件处理完成: ${file.name}`);
            return parsedData;

        } catch (error) {
            this.appState.failedFiles++;
            this.updateProgressUI();

            console.error(`[Main] 文件处理失败: ${file.name}`, error);
            throw error;
        }
    }

    // ==================== 生命周期方法 ====================

    /**
     * 销毁应用
     * @LIFECYCLE 应用销毁方法
     */
    destroy() {
        // 销毁所有管理器
        if (this.notificationManager) this.notificationManager.destroy();
        if (this.modalManager) this.modalManager.destroy();
        if (this.tabManager) this.tabManager.destroy();
        if (this.progressManager) this.progressManager.destroy();
        if (this.uploadManager) this.uploadManager.destroy();
        if (this.qaOptimizer) this.qaOptimizer.destroy();
        if (this.reportGenerator) this.reportGenerator.destroy();
        if (this.performanceMonitor) this.performanceMonitor.destroy();

        // 清理状态
        this.isInitialized = false;
        this.isProcessing = false;

        console.log('🗑️ MainApp 已销毁');
    }
}

// ==================== 全局实例和初始化函数 ====================

let mainAppInstance = null;

/**
 * 获取主应用实例
 * @SERVICE 主应用实例获取函数
 * @returns {MainApp} 主应用实例
 */
function getMainApp() {
    if (!mainAppInstance) {
        mainAppInstance = new MainApp();
    }
    return mainAppInstance;
}

/**
 * 初始化主应用
 * @SERVICE 主应用初始化函数
 * @returns {Promise<MainApp>} 初始化后的主应用实例
 */
async function init() {
    const app = getMainApp();
    const success = await app.initialize();

    if (success) {
        // 将实例暴露到全局作用域（兼容性）
        window.mainApp = app;
        return app;
    } else {
        throw new Error('主应用初始化失败');
    }
}

// 默认导出
// 将模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['main.js'] = {
    MainApp,
    TaskPool,
    getMainApp,
    init
};
