/**
 * ==================== GoMyHire 对话分析系统 - 进度显示系统 ====================
 * @SERVICE 从standalone.html完整迁移的进度显示系统
 * 实现进度条和状态显示，支持实时更新、动画效果、多种进度类型等功能
 */

import { generateUniqueId, safeExecute } from './utils.js';

// 进度类型常量
const PROGRESS_TYPES = {
    LINEAR: 'linear',
    CIRCULAR: 'circular',
    STEP: 'step',
    INDETERMINATE: 'indeterminate'
};

// 进度状态常量
const PROGRESS_STATES = {
    IDLE: 'idle',
    ACTIVE: 'active',
    PAUSED: 'paused',
    COMPLETED: 'completed',
    ERROR: 'error',
    CANCELLED: 'cancelled'
};

// 进度主题常量
const PROGRESS_THEMES = {
    PRIMARY: 'primary',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
    INFO: 'info'
};

// ==================== 进度管理器类 ====================
/**
 * 进度管理器类 - 负责管理所有进度条的显示、更新和生命周期
 * @COMPONENT 进度管理器
 */
class ProgressManager {
    constructor() {
        this.progressBars = new Map(); // 存储所有进度条实例
        this.globalProgress = null; // 全局进度条
        this.animationFrameId = null; // 动画帧ID
        this.updateInterval = null; // 更新间隔
        this.isAnimating = false; // 是否正在动画中
        
        console.log('📊 ProgressManager 初始化完成');
    }

    /**
     * 创建进度条
     * @SERVICE 进度条创建方法
     * @param {string} containerId - 容器ID
     * @param {Object} options - 进度条选项
     * @returns {string} 进度条ID
     */
    createProgress(containerId, options = {}) {
        const progressId = generateUniqueId('progress');
        const container = typeof containerId === 'string' ? 
            document.getElementById(containerId) : containerId;

        if (!container) {
            console.error(`进度条容器未找到: ${containerId}`);
            return null;
        }

        const config = {
            id: progressId,
            container: container,
            type: options.type || PROGRESS_TYPES.LINEAR,
            theme: options.theme || PROGRESS_THEMES.PRIMARY,
            value: options.value || 0,
            max: options.max || 100,
            min: options.min || 0,
            showText: options.showText !== false,
            showPercentage: options.showPercentage !== false,
            animated: options.animated !== false,
            striped: options.striped || false,
            height: options.height || '20px',
            label: options.label || '',
            onUpdate: options.onUpdate || null,
            onComplete: options.onComplete || null,
            state: PROGRESS_STATES.IDLE,
            createdAt: Date.now()
        };

        // 创建进度条元素
        const progressElement = this.createProgressElement(config);
        container.appendChild(progressElement);

        // 存储进度条实例
        this.progressBars.set(progressId, {
            config: config,
            element: progressElement,
            lastUpdate: Date.now()
        });

        console.log(`📊 进度条已创建: ${progressId}`);
        return progressId;
    }

    /**
     * 创建进度条元素
     * @SERVICE 进度条元素创建方法
     * @param {Object} config - 进度条配置
     * @returns {HTMLElement} 进度条元素
     */
    createProgressElement(config) {
        const wrapper = document.createElement('div');
        wrapper.className = `progress-wrapper progress-${config.type} progress-${config.theme}`;
        wrapper.id = `progress-wrapper-${config.id}`;

        // 基础样式
        wrapper.style.cssText = `
            margin-bottom: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        let html = '';

        // 添加标签
        if (config.label) {
            html += `
                <div class="progress-label" style="
                    margin-bottom: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    color: #374151;
                ">${config.label}</div>
            `;
        }

        // 根据类型创建不同的进度条
        switch (config.type) {
            case PROGRESS_TYPES.LINEAR:
                html += this.createLinearProgress(config);
                break;
            case PROGRESS_TYPES.CIRCULAR:
                html += this.createCircularProgress(config);
                break;
            case PROGRESS_TYPES.STEP:
                html += this.createStepProgress(config);
                break;
            case PROGRESS_TYPES.INDETERMINATE:
                html += this.createIndeterminateProgress(config);
                break;
            default:
                html += this.createLinearProgress(config);
        }

        // 添加状态信息
        if (config.showText || config.showPercentage) {
            html += `
                <div class="progress-info" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 8px;
                    font-size: 12px;
                    color: #6b7280;
                ">
                    <span class="progress-text" id="progress-text-${config.id}">
                        ${config.showText ? '准备中...' : ''}
                    </span>
                    <span class="progress-percentage" id="progress-percentage-${config.id}">
                        ${config.showPercentage ? '0%' : ''}
                    </span>
                </div>
            `;
        }

        wrapper.innerHTML = html;
        return wrapper;
    }

    /**
     * 创建线性进度条
     * @SERVICE 线性进度条创建方法
     * @param {Object} config - 进度条配置
     * @returns {string} HTML字符串
     */
    createLinearProgress(config) {
        const stripedClass = config.striped ? 'progress-striped' : '';
        const animatedClass = config.animated ? 'progress-animated' : '';

        return `
            <div class="progress-bar ${stripedClass} ${animatedClass}" style="
                width: 100%;
                height: ${config.height};
                background: ${this.getBackgroundColor(config.theme, 0.2)};
                border-radius: 10px;
                overflow: hidden;
                position: relative;
            ">
                <div class="progress-fill" id="progress-fill-${config.id}" style="
                    height: 100%;
                    width: 0%;
                    background: ${this.getProgressGradient(config.theme)};
                    border-radius: 10px;
                    transition: width 0.3s ease;
                    position: relative;
                    overflow: hidden;
                ">
                    ${config.striped ? this.getStripedPattern() : ''}
                </div>
            </div>
        `;
    }

    /**
     * 创建圆形进度条
     * @SERVICE 圆形进度条创建方法
     * @param {Object} config - 进度条配置
     * @returns {string} HTML字符串
     */
    createCircularProgress(config) {
        const size = parseInt(config.height) * 4; // 圆形进度条大小
        const strokeWidth = Math.max(4, size / 20);
        const radius = (size - strokeWidth) / 2;
        const circumference = 2 * Math.PI * radius;

        return `
            <div class="progress-circular" style="
                width: ${size}px;
                height: ${size}px;
                margin: 0 auto;
                position: relative;
            ">
                <svg width="${size}" height="${size}" style="transform: rotate(-90deg);">
                    <circle
                        cx="${size / 2}"
                        cy="${size / 2}"
                        r="${radius}"
                        stroke="${this.getBackgroundColor(config.theme, 0.2)}"
                        stroke-width="${strokeWidth}"
                        fill="none"
                    />
                    <circle
                        id="progress-circle-${config.id}"
                        cx="${size / 2}"
                        cy="${size / 2}"
                        r="${radius}"
                        stroke="${this.getThemeColor(config.theme)}"
                        stroke-width="${strokeWidth}"
                        fill="none"
                        stroke-dasharray="${circumference}"
                        stroke-dashoffset="${circumference}"
                        stroke-linecap="round"
                        style="transition: stroke-dashoffset 0.3s ease;"
                    />
                </svg>
                <div class="progress-circular-text" style="
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: ${size / 8}px;
                    font-weight: bold;
                    color: ${this.getThemeColor(config.theme)};
                ">0%</div>
            </div>
        `;
    }

    /**
     * 创建步骤进度条
     * @SERVICE 步骤进度条创建方法
     * @param {Object} config - 进度条配置
     * @returns {string} HTML字符串
     */
    createStepProgress(config) {
        const steps = config.steps || ['步骤1', '步骤2', '步骤3'];
        const stepWidth = 100 / steps.length;

        let html = `
            <div class="progress-steps" style="
                display: flex;
                align-items: center;
                position: relative;
                margin: 20px 0;
            ">
        `;

        steps.forEach((step, index) => {
            html += `
                <div class="progress-step" data-step="${index}" style="
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    position: relative;
                    z-index: 2;
                ">
                    <div class="step-circle" style="
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        background: ${this.getBackgroundColor(config.theme, 0.2)};
                        border: 2px solid ${this.getBackgroundColor(config.theme, 0.4)};
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        font-weight: bold;
                        color: #6b7280;
                        margin-bottom: 8px;
                    ">${index + 1}</div>
                    <div class="step-label" style="
                        font-size: 12px;
                        color: #6b7280;
                        text-align: center;
                        max-width: 80px;
                    ">${step}</div>
                </div>
            `;

            // 添加连接线（除了最后一个步骤）
            if (index < steps.length - 1) {
                html += `
                    <div class="step-connector" style="
                        flex: 1;
                        height: 2px;
                        background: ${this.getBackgroundColor(config.theme, 0.2)};
                        margin: 0 -16px;
                        margin-bottom: 24px;
                        z-index: 1;
                    "></div>
                `;
            }
        });

        html += '</div>';
        return html;
    }

    /**
     * 创建不确定进度条
     * @SERVICE 不确定进度条创建方法
     * @param {Object} config - 进度条配置
     * @returns {string} HTML字符串
     */
    createIndeterminateProgress(config) {
        return `
            <div class="progress-indeterminate" style="
                width: 100%;
                height: ${config.height};
                background: ${this.getBackgroundColor(config.theme, 0.2)};
                border-radius: 10px;
                overflow: hidden;
                position: relative;
            ">
                <div class="progress-indeterminate-fill" style="
                    height: 100%;
                    width: 30%;
                    background: ${this.getProgressGradient(config.theme)};
                    border-radius: 10px;
                    position: absolute;
                    animation: indeterminate 2s infinite linear;
                "></div>
            </div>
            <style>
                @keyframes indeterminate {
                    0% { left: -30%; }
                    100% { left: 100%; }
                }
            </style>
        `;
    }

    /**
     * 获取条纹图案
     * @UTIL 条纹图案获取工具
     * @returns {string} HTML字符串
     */
    getStripedPattern() {
        return `
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: linear-gradient(
                    45deg,
                    rgba(255,255,255,0.15) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255,255,255,0.15) 50%,
                    rgba(255,255,255,0.15) 75%,
                    transparent 75%,
                    transparent
                );
                background-size: 20px 20px;
                animation: progress-stripes 1s linear infinite;
            "></div>
            <style>
                @keyframes progress-stripes {
                    0% { background-position: 0 0; }
                    100% { background-position: 20px 0; }
                }
            </style>
        `;
    }

    /**
     * 获取主题颜色
     * @UTIL 主题颜色获取工具
     * @param {string} theme - 主题名称
     * @returns {string} 颜色值
     */
    getThemeColor(theme) {
        const colors = {
            [PROGRESS_THEMES.PRIMARY]: '#3b82f6',
            [PROGRESS_THEMES.SUCCESS]: '#10b981',
            [PROGRESS_THEMES.WARNING]: '#f59e0b',
            [PROGRESS_THEMES.ERROR]: '#ef4444',
            [PROGRESS_THEMES.INFO]: '#06b6d4'
        };
        return colors[theme] || colors[PROGRESS_THEMES.PRIMARY];
    }

    /**
     * 获取背景颜色
     * @UTIL 背景颜色获取工具
     * @param {string} theme - 主题名称
     * @param {number} opacity - 透明度
     * @returns {string} 颜色值
     */
    getBackgroundColor(theme, opacity = 0.2) {
        const baseColor = this.getThemeColor(theme);
        // 将十六进制转换为rgba
        const r = parseInt(baseColor.slice(1, 3), 16);
        const g = parseInt(baseColor.slice(3, 5), 16);
        const b = parseInt(baseColor.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }

    /**
     * 获取进度渐变
     * @UTIL 进度渐变获取工具
     * @param {string} theme - 主题名称
     * @returns {string} 渐变值
     */
    getProgressGradient(theme) {
        const baseColor = this.getThemeColor(theme);
        const colors = {
            [PROGRESS_THEMES.PRIMARY]: `linear-gradient(90deg, ${baseColor}, #1d4ed8)`,
            [PROGRESS_THEMES.SUCCESS]: `linear-gradient(90deg, ${baseColor}, #059669)`,
            [PROGRESS_THEMES.WARNING]: `linear-gradient(90deg, ${baseColor}, #d97706)`,
            [PROGRESS_THEMES.ERROR]: `linear-gradient(90deg, ${baseColor}, #dc2626)`,
            [PROGRESS_THEMES.INFO]: `linear-gradient(90deg, ${baseColor}, #0891b2)`
        };
        return colors[theme] || colors[PROGRESS_THEMES.PRIMARY];
    }

    /**
     * 更新进度条
     * @SERVICE 进度条更新方法
     * @param {string} progressId - 进度条ID
     * @param {number} value - 进度值
     * @param {string} text - 进度文本
     * @returns {boolean} 更新是否成功
     */
    updateProgress(progressId, value, text = '') {
        const progressBar = this.progressBars.get(progressId);
        if (!progressBar) {
            console.warn(`进度条不存在: ${progressId}`);
            return false;
        }

        const { config, element } = progressBar;
        const clampedValue = Math.max(config.min, Math.min(config.max, value));
        const percentage = ((clampedValue - config.min) / (config.max - config.min)) * 100;

        // 更新配置
        config.value = clampedValue;
        progressBar.lastUpdate = Date.now();

        // 根据类型更新不同的进度条
        switch (config.type) {
            case PROGRESS_TYPES.LINEAR:
                this.updateLinearProgress(progressId, percentage, text);
                break;
            case PROGRESS_TYPES.CIRCULAR:
                this.updateCircularProgress(progressId, percentage, text);
                break;
            case PROGRESS_TYPES.STEP:
                this.updateStepProgress(progressId, value, text);
                break;
        }

        // 更新状态
        if (percentage >= 100) {
            config.state = PROGRESS_STATES.COMPLETED;
            if (config.onComplete) {
                safeExecute(() => config.onComplete(progressId, clampedValue));
            }
        } else if (percentage > 0) {
            config.state = PROGRESS_STATES.ACTIVE;
        }

        // 触发更新回调
        if (config.onUpdate) {
            safeExecute(() => config.onUpdate(progressId, clampedValue, percentage));
        }

        return true;
    }

    /**
     * 更新线性进度条
     * @SERVICE 线性进度条更新方法
     * @param {string} progressId - 进度条ID
     * @param {number} percentage - 百分比
     * @param {string} text - 进度文本
     */
    updateLinearProgress(progressId, percentage, text) {
        const progressFill = document.getElementById(`progress-fill-${progressId}`);
        const progressText = document.getElementById(`progress-text-${progressId}`);
        const progressPercentage = document.getElementById(`progress-percentage-${progressId}`);

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText && text) {
            progressText.textContent = text;
        }

        if (progressPercentage) {
            progressPercentage.textContent = `${Math.round(percentage)}%`;
        }
    }

    /**
     * 更新圆形进度条
     * @SERVICE 圆形进度条更新方法
     * @param {string} progressId - 进度条ID
     * @param {number} percentage - 百分比
     * @param {string} text - 进度文本
     */
    updateCircularProgress(progressId, percentage, text) {
        const progressCircle = document.getElementById(`progress-circle-${progressId}`);
        const progressText = progressCircle?.parentElement?.querySelector('.progress-circular-text');

        if (progressCircle) {
            const circumference = 2 * Math.PI * parseFloat(progressCircle.getAttribute('r'));
            const offset = circumference - (percentage / 100) * circumference;
            progressCircle.style.strokeDashoffset = offset;
        }

        if (progressText) {
            progressText.textContent = text || `${Math.round(percentage)}%`;
        }
    }

    /**
     * 更新步骤进度条
     * @SERVICE 步骤进度条更新方法
     * @param {string} progressId - 进度条ID
     * @param {number} currentStep - 当前步骤
     * @param {string} text - 进度文本
     */
    updateStepProgress(progressId, currentStep, text) {
        const progressBar = this.progressBars.get(progressId);
        if (!progressBar) return;

        const element = progressBar.element;
        const steps = element.querySelectorAll('.progress-step');
        const connectors = element.querySelectorAll('.step-connector');

        steps.forEach((step, index) => {
            const circle = step.querySelector('.step-circle');
            const label = step.querySelector('.step-label');

            if (index < currentStep) {
                // 已完成的步骤
                circle.style.background = this.getThemeColor(progressBar.config.theme);
                circle.style.borderColor = this.getThemeColor(progressBar.config.theme);
                circle.style.color = 'white';
                circle.innerHTML = '✓';
            } else if (index === currentStep) {
                // 当前步骤
                circle.style.background = this.getThemeColor(progressBar.config.theme);
                circle.style.borderColor = this.getThemeColor(progressBar.config.theme);
                circle.style.color = 'white';
                circle.textContent = index + 1;
                if (text) {
                    label.textContent = text;
                }
            } else {
                // 未完成的步骤
                circle.style.background = this.getBackgroundColor(progressBar.config.theme, 0.2);
                circle.style.borderColor = this.getBackgroundColor(progressBar.config.theme, 0.4);
                circle.style.color = '#6b7280';
                circle.textContent = index + 1;
            }
        });

        // 更新连接线
        connectors.forEach((connector, index) => {
            if (index < currentStep) {
                connector.style.background = this.getThemeColor(progressBar.config.theme);
            } else {
                connector.style.background = this.getBackgroundColor(progressBar.config.theme, 0.2);
            }
        });
    }

    /**
     * 设置进度条状态
     * @SERVICE 进度条状态设置方法
     * @param {string} progressId - 进度条ID
     * @param {string} state - 状态
     * @returns {boolean} 设置是否成功
     */
    setState(progressId, state) {
        const progressBar = this.progressBars.get(progressId);
        if (!progressBar) {
            return false;
        }

        progressBar.config.state = state;
        const element = progressBar.element;

        // 移除所有状态类
        element.classList.remove('state-idle', 'state-active', 'state-paused', 'state-completed', 'state-error', 'state-cancelled');
        
        // 添加新状态类
        element.classList.add(`state-${state}`);

        return true;
    }

    /**
     * 暂停进度条
     * @SERVICE 进度条暂停方法
     * @param {string} progressId - 进度条ID
     * @returns {boolean} 暂停是否成功
     */
    pauseProgress(progressId) {
        return this.setState(progressId, PROGRESS_STATES.PAUSED);
    }

    /**
     * 恢复进度条
     * @SERVICE 进度条恢复方法
     * @param {string} progressId - 进度条ID
     * @returns {boolean} 恢复是否成功
     */
    resumeProgress(progressId) {
        return this.setState(progressId, PROGRESS_STATES.ACTIVE);
    }

    /**
     * 完成进度条
     * @SERVICE 进度条完成方法
     * @param {string} progressId - 进度条ID
     * @param {string} text - 完成文本
     * @returns {boolean} 完成是否成功
     */
    completeProgress(progressId, text = '已完成') {
        const success = this.updateProgress(progressId, 100, text);
        if (success) {
            this.setState(progressId, PROGRESS_STATES.COMPLETED);
        }
        return success;
    }

    /**
     * 设置进度条错误状态
     * @SERVICE 进度条错误状态设置方法
     * @param {string} progressId - 进度条ID
     * @param {string} errorText - 错误文本
     * @returns {boolean} 设置是否成功
     */
    setError(progressId, errorText = '处理失败') {
        const progressBar = this.progressBars.get(progressId);
        if (!progressBar) {
            return false;
        }

        // 更新文本
        const progressText = document.getElementById(`progress-text-${progressId}`);
        if (progressText) {
            progressText.textContent = errorText;
        }

        // 设置错误状态
        return this.setState(progressId, PROGRESS_STATES.ERROR);
    }

    /**
     * 移除进度条
     * @SERVICE 进度条移除方法
     * @param {string} progressId - 进度条ID
     * @returns {boolean} 移除是否成功
     */
    removeProgress(progressId) {
        const progressBar = this.progressBars.get(progressId);
        if (!progressBar) {
            return false;
        }

        // 从DOM中移除
        if (progressBar.element.parentNode) {
            progressBar.element.parentNode.removeChild(progressBar.element);
        }

        // 从存储中移除
        this.progressBars.delete(progressId);

        console.log(`📊 进度条已移除: ${progressId}`);
        return true;
    }

    /**
     * 获取进度条信息
     * @SERVICE 进度条信息获取方法
     * @param {string} progressId - 进度条ID
     * @returns {Object|null} 进度条信息
     */
    getProgressInfo(progressId) {
        const progressBar = this.progressBars.get(progressId);
        if (!progressBar) {
            return null;
        }

        const { config } = progressBar;
        return {
            id: progressId,
            type: config.type,
            theme: config.theme,
            value: config.value,
            max: config.max,
            min: config.min,
            percentage: ((config.value - config.min) / (config.max - config.min)) * 100,
            state: config.state,
            createdAt: config.createdAt,
            lastUpdate: progressBar.lastUpdate
        };
    }

    /**
     * 获取所有进度条
     * @SERVICE 所有进度条获取方法
     * @returns {Array} 进度条列表
     */
    getAllProgress() {
        return Array.from(this.progressBars.keys());
    }

    /**
     * 清空所有进度条
     * @SERVICE 所有进度条清空方法
     */
    clearAll() {
        const progressIds = Array.from(this.progressBars.keys());
        progressIds.forEach(id => this.removeProgress(id));
    }

    /**
     * 销毁进度管理器
     * @LIFECYCLE 进度管理器销毁方法
     */
    destroy() {
        this.clearAll();
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        console.log('🗑️ ProgressManager 已销毁');
    }
}

// ==================== 分析进度管理器类 ====================
/**
 * 分析进度管理器类 - 负责分析进度的实时保存和恢复
 * @MANAGER 分析进度管理器
 */
class AnalysisProgressManager {
    constructor(storageManager = null) {
        this.storageManager = storageManager;
        this.progress = this.loadProgress();
        this.sessionId = null;
        this.startTime = null;
        this.isRunning = false;

        console.log('📈 AnalysisProgressManager 初始化完成');
    }

    /**
     * 加载分析进度
     * @SERVICE 分析进度加载方法
     * @returns {Object} 进度数据
     */
    loadProgress() {
        if (!this.storageManager) {
            return this.getDefaultProgress();
        }

        return this.storageManager.load('analysis_progress', this.getDefaultProgress());
    }

    /**
     * 获取默认进度数据
     * @UTIL 默认进度数据获取工具
     * @returns {Object} 默认进度数据
     */
    getDefaultProgress() {
        return {
            sessionId: null,
            startTime: null,
            totalFiles: 0,
            processedFiles: 0,
            completedFiles: 0,
            failedFiles: 0,
            currentBatch: [],
            isRunning: false,
            lastSaveTime: null,
            currentFile: '',
            currentStep: '',
            estimatedTimeRemaining: 0
        };
    }

    /**
     * 保存分析进度
     * @SERVICE 分析进度保存方法
     * @returns {boolean} 保存是否成功
     */
    saveProgress() {
        if (!this.storageManager) {
            return false;
        }

        this.progress.lastSaveTime = new Date().toISOString();
        return this.storageManager.save('analysis_progress', this.progress);
    }

    /**
     * 开始新的分析会话
     * @SERVICE 分析会话开始方法
     * @param {number} totalFiles - 总文件数
     * @returns {string} 会话ID
     */
    startSession(totalFiles) {
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
        this.isRunning = true;

        this.progress = {
            ...this.getDefaultProgress(),
            sessionId: this.sessionId,
            startTime: new Date().toISOString(),
            totalFiles: totalFiles,
            isRunning: true
        };

        this.saveProgress();
        console.log(`📈 分析会话开始: ${this.sessionId}, 总文件数: ${totalFiles}`);
        return this.sessionId;
    }

    /**
     * 更新进度
     * @SERVICE 进度更新方法
     * @param {Object} data - 更新数据
     */
    updateProgress(data) {
        Object.assign(this.progress, data);
        this.saveProgress();

        // 计算预计剩余时间
        if (this.progress.processedFiles > 0 && this.startTime) {
            const elapsed = Date.now() - this.startTime;
            const avgTimePerFile = elapsed / this.progress.processedFiles;
            const remainingFiles = this.progress.totalFiles - this.progress.processedFiles;
            this.progress.estimatedTimeRemaining = Math.round(avgTimePerFile * remainingFiles);
        }
    }

    /**
     * 完成会话
     * @SERVICE 分析会话完成方法
     */
    completeSession() {
        this.progress.isRunning = false;
        this.progress.endTime = new Date().toISOString();
        this.isRunning = false;
        this.saveProgress();
        console.log(`✅ 分析会话完成: ${this.progress.sessionId}`);
    }

    /**
     * 检查是否有未完成的会话
     * @SERVICE 未完成会话检查方法
     * @returns {boolean} 是否有未完成的会话
     */
    hasUnfinishedSession() {
        return this.progress.isRunning && this.progress.sessionId;
    }

    /**
     * 获取进度信息
     * @SERVICE 进度信息获取方法
     * @returns {Object} 进度信息
     */
    getProgressInfo() {
        const percentage = this.progress.totalFiles > 0 ?
            Math.round((this.progress.processedFiles / this.progress.totalFiles) * 100) : 0;

        return {
            ...this.progress,
            percentage: percentage,
            remainingFiles: this.progress.totalFiles - this.progress.processedFiles,
            elapsedTime: this.startTime ? Date.now() - this.startTime : 0,
            isRunning: this.isRunning
        };
    }

    /**
     * 获取进度百分比
     * @SERVICE 进度百分比获取方法
     * @returns {number} 进度百分比
     */
    getProgressPercentage() {
        if (this.progress.totalFiles === 0) return 0;
        return Math.round((this.progress.processedFiles / this.progress.totalFiles) * 100);
    }

    /**
     * 重置进度
     * @SERVICE 进度重置方法
     */
    resetProgress() {
        this.progress = this.getDefaultProgress();
        this.sessionId = null;
        this.startTime = null;
        this.isRunning = false;
        this.saveProgress();
        console.log('🔄 分析进度已重置');
    }

    /**
     * 生成会话ID
     * @FACTORY 会话ID生成工厂
     * @returns {string} 会话ID
     */
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 格式化剩余时间
     * @UTIL 剩余时间格式化工具
     * @param {number} milliseconds - 毫秒数
     * @returns {string} 格式化的时间字符串
     */
    formatRemainingTime(milliseconds) {
        if (!milliseconds || milliseconds <= 0) {
            return '计算中...';
        }

        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `约 ${hours} 小时 ${minutes % 60} 分钟`;
        } else if (minutes > 0) {
            return `约 ${minutes} 分钟 ${seconds % 60} 秒`;
        } else {
            return `约 ${seconds} 秒`;
        }
    }
}

// ==================== 处理状态UI管理器类 ====================
/**
 * 处理状态UI管理器类 - 负责处理状态的UI显示和更新
 * @COMPONENT 处理状态UI管理器
 */
class ProcessingStatusUI {
    constructor() {
        this.isVisible = false;
        this.statusElement = null;
        this.updateInterval = null;
        this.components = new Map();

        this.initialize();
        console.log('🎛️ ProcessingStatusUI 初始化完成');
    }

    /**
     * 初始化状态UI
     * @INIT 状态UI初始化方法
     */
    initialize() {
        this.createStatusElement();
        this.setupEventListeners();
    }

    /**
     * 创建状态元素
     * @SERVICE 状态元素创建方法
     */
    createStatusElement() {
        // 检查是否已存在
        this.statusElement = document.getElementById('processing-status-panel');
        if (this.statusElement) {
            return;
        }

        // 创建状态面板
        this.statusElement = document.createElement('div');
        this.statusElement.id = 'processing-status-panel';
        this.statusElement.className = 'processing-status-panel';

        this.statusElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            z-index: 9998;
            display: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        this.statusElement.innerHTML = `
            <div class="status-panel-header" style="
                padding: 16px 20px;
                border-bottom: 1px solid #f3f4f6;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #111827;">
                    <i class="fas fa-cogs" style="margin-right: 8px; color: #3b82f6;"></i>
                    处理状态
                </h3>
                <button type="button" class="status-close-btn" style="
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: #6b7280;
                    padding: 4px;
                    border-radius: 4px;
                ">&times;</button>
            </div>
            <div class="status-panel-body" style="padding: 20px;">
                <!-- 总体进度 -->
                <div class="progress-info" style="margin-bottom: 20px;">
                    <div class="progress-bar" style="
                        width: 100%;
                        height: 8px;
                        background: #f3f4f6;
                        border-radius: 4px;
                        overflow: hidden;
                        margin-bottom: 8px;
                    ">
                        <div id="overall-progress-fill" class="progress-fill" style="
                            width: 0%;
                            height: 100%;
                            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                            transition: width 0.3s ease;
                        "></div>
                    </div>
                    <div class="progress-text" style="
                        display: flex;
                        justify-content: space-between;
                        font-size: 12px;
                        color: #6b7280;
                    ">
                        <span id="progress-text">准备中...</span>
                        <span id="progress-percentage">0%</span>
                    </div>
                </div>

                <!-- 当前文件信息 -->
                <div class="current-file-info" style="
                    background: #f9fafb;
                    padding: 12px;
                    border-radius: 6px;
                    margin-bottom: 16px;
                    font-size: 13px;
                ">
                    <div style="color: #374151; font-weight: 500; margin-bottom: 4px;">当前处理:</div>
                    <div id="current-file" style="color: #6b7280; word-break: break-all;">等待开始...</div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-grid" style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                    margin-bottom: 16px;
                ">
                    <div class="stat-item" style="
                        text-align: center;
                        padding: 8px;
                        background: #f0f9ff;
                        border-radius: 6px;
                    ">
                        <div id="completed-count" style="
                            font-size: 18px;
                            font-weight: bold;
                            color: #0369a1;
                        ">0</div>
                        <div style="font-size: 11px; color: #0369a1;">已完成</div>
                    </div>
                    <div class="stat-item" style="
                        text-align: center;
                        padding: 8px;
                        background: #f3f4f6;
                        border-radius: 6px;
                    ">
                        <div id="total-count" style="
                            font-size: 18px;
                            font-weight: bold;
                            color: #374151;
                        ">0</div>
                        <div style="font-size: 11px; color: #374151;">总计</div>
                    </div>
                </div>

                <!-- 预计剩余时间 -->
                <div class="time-info" style="
                    text-align: center;
                    font-size: 12px;
                    color: #6b7280;
                    padding: 8px;
                    background: #fefce8;
                    border-radius: 6px;
                ">
                    <span>预计剩余时间: </span>
                    <span id="estimated-time">计算中...</span>
                </div>
            </div>
        `;

        document.body.appendChild(this.statusElement);
    }

    /**
     * 设置事件监听器
     * @SERVICE 事件监听器设置方法
     */
    setupEventListeners() {
        // 关闭按钮事件
        const closeBtn = this.statusElement.querySelector('.status-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hide();
            });
        }

        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.statusElement.contains(e.target)) {
                // 可选：点击外部不关闭，只有点击关闭按钮才关闭
                // this.hide();
            }
        });
    }

    /**
     * 显示状态面板
     * @SERVICE 状态面板显示方法
     */
    show() {
        if (this.statusElement) {
            this.statusElement.style.display = 'block';
            this.isVisible = true;

            // 开始定期更新
            this.startUpdating();
        }
    }

    /**
     * 隐藏状态面板
     * @SERVICE 状态面板隐藏方法
     */
    hide() {
        if (this.statusElement) {
            this.statusElement.style.display = 'none';
            this.isVisible = false;

            // 停止定期更新
            this.stopUpdating();
        }
    }

    /**
     * 切换状态面板显示
     * @SERVICE 状态面板切换方法
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 开始定期更新
     * @SERVICE 定期更新开始方法
     */
    startUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        this.updateInterval = setInterval(() => {
            this.updateStatus();
        }, 1000); // 每秒更新一次
    }

    /**
     * 停止定期更新
     * @SERVICE 定期更新停止方法
     */
    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * 更新状态显示
     * @SERVICE 状态显示更新方法
     */
    updateStatus() {
        if (!this.isVisible) return;

        // 这里可以从全局状态管理器获取数据
        // 暂时使用模拟数据
        const progressInfo = this.getProgressInfo();

        this.updateProgressBar(progressInfo.percentage, progressInfo.text);
        this.updateCurrentFile(progressInfo.currentFile);
        this.updateStats(progressInfo.completed, progressInfo.total);
        this.updateEstimatedTime(progressInfo.estimatedTime);
    }

    /**
     * 更新进度条
     * @SERVICE 进度条更新方法
     * @param {number} percentage - 百分比
     * @param {string} text - 进度文本
     */
    updateProgressBar(percentage, text) {
        const progressFill = this.statusElement.querySelector('#overall-progress-fill');
        const progressText = this.statusElement.querySelector('#progress-text');
        const progressPercentage = this.statusElement.querySelector('#progress-percentage');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText && text) {
            progressText.textContent = text;
        }

        if (progressPercentage) {
            progressPercentage.textContent = `${Math.round(percentage)}%`;
        }
    }

    /**
     * 更新当前文件信息
     * @SERVICE 当前文件信息更新方法
     * @param {string} fileName - 文件名
     */
    updateCurrentFile(fileName) {
        const currentFileEl = this.statusElement.querySelector('#current-file');
        if (currentFileEl && fileName) {
            currentFileEl.textContent = fileName;
        }
    }

    /**
     * 更新统计信息
     * @SERVICE 统计信息更新方法
     * @param {number} completed - 已完成数量
     * @param {number} total - 总数量
     */
    updateStats(completed, total) {
        const completedEl = this.statusElement.querySelector('#completed-count');
        const totalEl = this.statusElement.querySelector('#total-count');

        if (completedEl) {
            completedEl.textContent = completed || 0;
        }

        if (totalEl) {
            totalEl.textContent = total || 0;
        }
    }

    /**
     * 更新预计时间
     * @SERVICE 预计时间更新方法
     * @param {string} timeText - 时间文本
     */
    updateEstimatedTime(timeText) {
        const estimatedTimeEl = this.statusElement.querySelector('#estimated-time');
        if (estimatedTimeEl && timeText) {
            estimatedTimeEl.textContent = timeText;
        }
    }

    /**
     * 获取进度信息（模拟数据，实际应从全局状态管理器获取）
     * @SERVICE 进度信息获取方法
     * @returns {Object} 进度信息
     */
    getProgressInfo() {
        // 这里应该从全局状态管理器获取真实数据
        // 暂时返回模拟数据
        return {
            percentage: 0,
            text: '准备中...',
            currentFile: '等待开始...',
            completed: 0,
            total: 0,
            estimatedTime: '计算中...'
        };
    }

    /**
     * 销毁状态UI
     * @LIFECYCLE 状态UI销毁方法
     */
    destroy() {
        this.stopUpdating();

        if (this.statusElement && this.statusElement.parentNode) {
            this.statusElement.parentNode.removeChild(this.statusElement);
        }

        this.components.clear();
        console.log('🗑️ ProcessingStatusUI 已销毁');
    }
}

// ==================== 全局进度管理器实例 ====================
let globalProgressManager = null;
let globalAnalysisProgressManager = null;
let globalProcessingStatusUI = null;

/**
 * 获取全局进度管理器实例
 * @SERVICE 全局进度管理器获取函数
 * @returns {ProgressManager} 进度管理器实例
 */
function getProgressManager() {
    if (!globalProgressManager) {
        globalProgressManager = new ProgressManager();
    }
    return globalProgressManager;
}

/**
 * 获取全局分析进度管理器实例
 * @SERVICE 全局分析进度管理器获取函数
 * @param {Object} storageManager - 存储管理器
 * @returns {AnalysisProgressManager} 分析进度管理器实例
 */
function getAnalysisProgressManager(storageManager = null) {
    if (!globalAnalysisProgressManager) {
        globalAnalysisProgressManager = new AnalysisProgressManager(storageManager);
    }
    return globalAnalysisProgressManager;
}

/**
 * 获取全局处理状态UI实例
 * @SERVICE 全局处理状态UI获取函数
 * @returns {ProcessingStatusUI} 处理状态UI实例
 */
function getProcessingStatusUI() {
    if (!globalProcessingStatusUI) {
        globalProcessingStatusUI = new ProcessingStatusUI();
    }
    return globalProcessingStatusUI;
}

// ==================== 便捷函数 ====================

/**
 * 创建进度条
 * @SERVICE 进度条创建函数
 * @param {string} containerId - 容器ID
 * @param {Object} options - 进度条选项
 * @returns {string} 进度条ID
 */
function createProgress(containerId, options = {}) {
    return getProgressManager().createProgress(containerId, options);
}

/**
 * 更新进度条
 * @SERVICE 进度条更新函数
 * @param {string} progressId - 进度条ID
 * @param {number} value - 进度值
 * @param {string} text - 进度文本
 * @returns {boolean} 更新是否成功
 */
function updateProgress(progressId, value, text = '') {
    return getProgressManager().updateProgress(progressId, value, text);
}

/**
 * 完成进度条
 * @SERVICE 进度条完成函数
 * @param {string} progressId - 进度条ID
 * @param {string} text - 完成文本
 * @returns {boolean} 完成是否成功
 */
function completeProgress(progressId, text = '已完成') {
    return getProgressManager().completeProgress(progressId, text);
}

/**
 * 设置进度条错误状态
 * @SERVICE 进度条错误状态设置函数
 * @param {string} progressId - 进度条ID
 * @param {string} errorText - 错误文本
 * @returns {boolean} 设置是否成功
 */
function setProgressError(progressId, errorText = '处理失败') {
    return getProgressManager().setError(progressId, errorText);
}

/**
 * 移除进度条
 * @SERVICE 进度条移除函数
 * @param {string} progressId - 进度条ID
 * @returns {boolean} 移除是否成功
 */
function removeProgress(progressId) {
    return getProgressManager().removeProgress(progressId);
}

/**
 * 显示处理状态
 * @SERVICE 处理状态显示函数
 */
function showProcessingStatus() {
    getProcessingStatusUI().show();
}

/**
 * 隐藏处理状态
 * @SERVICE 处理状态隐藏函数
 */
function hideProcessingStatus() {
    getProcessingStatusUI().hide();
}

/**
 * 切换处理状态显示
 * @SERVICE 处理状态切换函数
 */
function toggleProcessingStatus() {
    getProcessingStatusUI().toggle();
}

// ==================== 兼容性函数 ====================

/**
 * 更新进度（兼容旧版本）
 * @SERVICE 进度更新函数
 * @param {number} percent - 百分比
 * @param {string} text - 进度文本
 * @param {string} currentFile - 当前文件
 * @param {number} completed - 已完成数量
 * @param {number} total - 总数量
 */
function updateProgressCompat(percent, text, currentFile = '', completed = 0, total = 0) {
    // 更新全局进度条元素（如果存在）
    const progressFill = document.getElementById('overall-progress-fill');
    const progressText = document.getElementById('progress-text');
    const progressPercentage = document.getElementById('progress-percentage');
    const currentFileEl = document.getElementById('current-file');
    const completedCountEl = document.getElementById('completed-count');
    const totalCountEl = document.getElementById('total-count');

    if (progressFill) {
        progressFill.style.width = `${percent}%`;
    }

    if (progressText) {
        progressText.textContent = text;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${percent}%`;
    }

    if (currentFileEl && currentFile) {
        currentFileEl.textContent = currentFile;
    }

    if (completedCountEl) {
        completedCountEl.textContent = completed;
    }

    if (totalCountEl) {
        totalCountEl.textContent = total;
    }

    // 同时更新上传进度条（如果存在）
    const uploadProgressFill = document.getElementById('upload-progress-fill');
    const uploadProgressText = document.getElementById('upload-progress-text');

    if (uploadProgressFill) {
        uploadProgressFill.style.width = `${percent}%`;
    }

    if (uploadProgressText) {
        uploadProgressText.textContent = text;
    }
}

/**
 * 显示/隐藏进度条（兼容旧版本）
 * @SERVICE 进度条显示隐藏函数
 * @param {boolean} show - 是否显示
 */
function showProgress(show) {
    const progressCard = document.getElementById('progress-card');
    const pauseBtn = document.getElementById('pause-btn');
    const resumeBtn = document.getElementById('resume-btn');

    if (progressCard) {
        progressCard.style.display = show ? 'block' : 'none';
    }

    if (show) {
        if (pauseBtn) pauseBtn.style.display = 'inline-block';
        if (resumeBtn) resumeBtn.style.display = 'none';
    } else {
        if (pauseBtn) pauseBtn.style.display = 'none';
        if (resumeBtn) resumeBtn.style.display = 'none';
    }
}

/**
 * 更新优化进度（兼容旧版本）
 * @SERVICE 优化进度更新函数
 * @param {Object} progress - 进度信息
 */
function updateOptimizationProgress(progress) {
    const progressFill = document.getElementById('optimization-progress-fill');
    const progressPercentage = document.getElementById('optimization-progress-percentage');
    const currentStep = document.getElementById('optimization-current-step');
    const stage = document.getElementById('optimization-stage');
    const status = document.getElementById('optimization-status');

    if (progressFill) {
        progressFill.style.width = `${progress.percentage}%`;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${progress.percentage}%`;
    }

    if (currentStep) {
        currentStep.textContent = progress.step || '处理中...';
    }

    if (stage) {
        stage.textContent = progress.stage || '处理中';
    }

    if (status) {
        status.textContent = progress.status || '正在处理';
    }
}

/**
 * 处理进度更新事件（兼容旧版本）
 * @SERVICE 进度更新事件处理函数
 * @param {Event} event - 进度更新事件
 */
function handleProgressUpdate(event) {
    const { progress, step } = event.detail;

    const progressFill = document.getElementById('report-progress-fill');
    const progressText = document.getElementById('report-progress-text');
    const progressPercentage = document.getElementById('report-progress-percentage');
    const currentStep = document.getElementById('report-current-step');

    if (progressFill) {
        progressFill.style.width = `${progress}%`;
    }

    if (progressText) {
        progressText.textContent = step;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${progress}%`;
    }

    if (currentStep) {
        currentStep.textContent = step;
    }
}

/**
 * 初始化状态UI（兼容旧版本）
 * @SERVICE 状态UI初始化函数
 */
function initializeStatusUI() {
    getProcessingStatusUI();
    console.log('✓ 状态UI初始化完成');
}

/**
 * 创建文件进度条（兼容旧版本）
 * @SERVICE 文件进度条创建函数
 * @param {string} containerId - 容器ID
 * @param {string} label - 标签
 * @returns {HTMLElement|null} 进度条元素
 */
function createFileProgressBar(containerId, label = '处理进度') {
    const container = document.getElementById(containerId);
    if (!container) return null;

    const progressContainer = document.createElement('div');
    progressContainer.className = 'file-progress';
    progressContainer.innerHTML = `
        <div class="file-progress-bar" style="
            width: 100%;
            height: 6px;
            background: #f3f4f6;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        ">
            <div class="file-progress-fill" style="
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                transition: width 0.3s ease;
            "></div>
        </div>
        <div class="file-progress-text" style="
            font-size: 12px;
            color: #6b7280;
            text-align: center;
        ">${label}: 0%</div>
    `;

    // 插入到文件列表前面
    const fileList = container.querySelector('.file-list');
    if (fileList) {
        fileList.insertBefore(progressContainer, fileList.firstChild);
    } else {
        container.appendChild(progressContainer);
    }

    return progressContainer;
}

/**
 * 更新文件进度条（兼容旧版本）
 * @SERVICE 文件进度条更新函数
 * @param {HTMLElement} progressContainer - 进度条容器
 * @param {number} current - 当前进度
 * @param {number} total - 总数
 * @param {string} label - 标签
 */
function updateFileProgressBar(progressContainer, current, total, label = '处理进度') {
    if (!progressContainer) return;

    const percentage = Math.round((current / total) * 100);
    const progressBar = progressContainer.querySelector('.file-progress-fill');
    const progressText = progressContainer.querySelector('.file-progress-text');

    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
    }

    if (progressText) {
        progressText.textContent = `${label}: ${current}/${total} (${percentage}%)`;
    }
}

/**
 * 移除文件进度条（兼容旧版本）
 * @SERVICE 文件进度条移除函数
 * @param {HTMLElement} progressContainer - 进度条容器
 */
function removeFileProgressBar(progressContainer) {
    if (progressContainer && progressContainer.parentNode) {
        progressContainer.parentNode.removeChild(progressContainer);
    }
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['progress.js'] = { ProgressManager, getProgressManager };
