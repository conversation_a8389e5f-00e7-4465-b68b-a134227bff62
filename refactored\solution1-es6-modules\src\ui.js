/**
 * ==================== GoMyHire 对话分析系统 - UI交互管理模块 ====================
 * 
 * @MODULE_INFO
 * 模块名称: UI Manager System
 * 版本: 2.0.0
 * 功能描述: 从standalone.html迁移的完整UI功能，管理所有UI交互和界面状态
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: DOM API, 浏览器事件系统
 * 间接依赖: storage.js (数据存储), parser.js (数据解析)
 * 被依赖: main.js, 各类UI组件
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 2 (第三层，UI管理模块)
 * 加载时机: after-dom (页面DOM加载后)
 * 加载条件: DOM准备就绪，基础模块加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - UI管理器类 (UIManager类)
 *   - 标签页导航管理
 *   - 通知系统
 *   - 模态框管理
 *   - 文件列表控制
 *   - 进度追踪
 * 导出接口: UIManager, getUIManager
 * 全局注册: window.ModuleExports['ui.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 用户交互事件、系统状态变化
 * 输出数据: UI状态更新、用户操作事件
 * 状态管理: 维护UI组件状态、活跃标签页
 * 事件处理: UI交互事件、系统状态事件
 * 
 * @INTEGRATION (集成关系)
 * DOM集成: 与页面DOM元素深度集成
 * 数据集成: 与数据存储和处理模块集成
 * 事件集成: 与全局事件系统集成
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 低 (轻量级UI管理)
 * 加载性能: 快速 (无重外部依赖)
 * 运行时性能: 高效 (优化的DOM操作)
 */

class UIManager {
    constructor() {
        this.activeTab = 'analysis';
        this.notifications = [];
        this.modals = new Map();
        this.fileListState = {
            currentPage: 1,
            itemsPerPage: 20,
            totalItems: 0,
            sortBy: 'uploadTime-desc',
            filterBy: '',
            searchQuery: ''
        };
        
        this.init();
    }

    /**
     * 初始化UI管理器
     */
    init() {
        this.setupTabNavigation();
        this.setupNotificationSystem();
        this.setupModalSystem();
        this.setupFileListControls();
        this.setupProgressTracking();
        this.setupKeyboardShortcuts();
    }

    /**
     * 设置标签页导航
     */
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetTab = e.currentTarget.dataset.tab;
                this.switchTab(targetTab);
            });
        });
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
            btn.setAttribute('aria-selected', 'false');
        });
        
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).setAttribute('aria-selected', 'true');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        const targetContent = document.getElementById(`${tabName}-panel`);
        if (targetContent) {
            targetContent.classList.add('active');
        }

        this.activeTab = tabName;
        
        // 触发标签页切换事件
        this.dispatchEvent('tabChanged', { tab: tabName });
    }

    /**
     * 设置通知系统
     */
    setupNotificationSystem() {
        // 创建通知容器
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `message-toast message-${type}`;
        notification.style.pointerEvents = 'auto';
        
        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <i class="fas ${icon}"></i>
            <span>${message}</span>
            <button class="message-close" onclick="this.parentElement.remove()">×</button>
        `;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        }

        this.notifications.push({
            element: notification,
            message,
            type,
            timestamp: Date.now()
        });
    }

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * 设置模态框系统
     */
    setupModalSystem() {
        // 监听ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * 显示模态框
     */
    showModal(title, content, options = {}) {
        const modalId = `modal-${Date.now()}`;
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = modalId;
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="window.uiManager.closeModal('${modalId}')">×</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                ${options.footer ? `<div class="modal-footer">${options.footer}</div>` : ''}
            </div>
        `;

        document.body.appendChild(modal);
        this.modals.set(modalId, modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modalId);
            }
        });

        return modalId;
    }

    /**
     * 关闭模态框
     */
    closeModal(modalId) {
        const modal = this.modals.get(modalId);
        if (modal) {
            modal.remove();
            this.modals.delete(modalId);
        }
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        this.modals.forEach((modal, modalId) => {
            this.closeModal(modalId);
        });
    }

    /**
     * 设置文件列表控制
     */
    setupFileListControls() {
        // 搜索功能
        const searchInput = document.getElementById('file-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.fileListState.searchQuery = e.target.value;
                this.updateFileList();
            }, 300));
        }

        // 状态过滤
        const statusFilter = document.getElementById('file-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.fileListState.filterBy = e.target.value;
                this.updateFileList();
            });
        }

        // 排序
        const sortSelect = document.getElementById('file-sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.fileListState.sortBy = e.target.value;
                this.updateFileList();
            });
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-file-list-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshFileList();
            });
        }

        // 清空按钮
        const clearBtn = document.getElementById('clear-all-files-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearAllFiles();
            });
        }
    }

    /**
     * 设置进度跟踪
     */
    setupProgressTracking() {
        this.progressElements = {
            container: document.getElementById('upload-status'),
            title: document.getElementById('upload-status-title'),
            fill: document.getElementById('upload-progress-fill'),
            text: document.getElementById('upload-progress-text'),
            cancelBtn: document.getElementById('upload-cancel-btn')
        };

        if (this.progressElements.cancelBtn) {
            this.progressElements.cancelBtn.addEventListener('click', () => {
                this.cancelCurrentOperation();
            });
        }
    }

    /**
     * 更新进度显示
     */
    updateProgress(current, total, message = '') {
        if (!this.progressElements.container) return;

        const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
        
        this.progressElements.container.classList.remove('hidden');
        
        if (this.progressElements.fill) {
            this.progressElements.fill.style.width = `${percentage}%`;
        }
        
        if (this.progressElements.text) {
            this.progressElements.text.textContent = message || `${current}/${total} (${percentage}%)`;
        }
        
        if (this.progressElements.title) {
            this.progressElements.title.textContent = current >= total ? '处理完成' : '处理中...';
        }
    }

    /**
     * 隐藏进度显示
     */
    hideProgress() {
        if (this.progressElements.container) {
            this.progressElements.container.classList.add('hidden');
        }
    }

    /**
     * 设置键盘快捷键
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+1-3 切换标签页
            if (e.ctrlKey && e.key >= '1' && e.key <= '3') {
                e.preventDefault();
                const tabs = ['analysis', 'reports', 'qa-dataset'];
                this.switchTab(tabs[parseInt(e.key) - 1]);
            }
            
            // Ctrl+R 刷新文件列表
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.refreshFileList();
            }
        });
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 分发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * 更新统计显示
     */
    updateStats(stats) {
        const statElements = {
            'main-drivers-count': stats.driversCount || 0,
            'main-agents-count': stats.agentsCount || 0,
            'main-knowledge-count': stats.knowledgeCount || 0,
            'main-qa-count': stats.qaCount || 0,
            'main-avg-satisfaction': stats.avgSatisfaction || 0,
            'main-avg-effectiveness': stats.avgEffectiveness || 0,
            'main-avg-response-time': stats.avgResponseTime || 0,
            'main-common-qa-count': stats.commonQACount || 0
        };

        Object.entries(statElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = typeof value === 'number' ? value.toFixed(1) : value;
            }
        });
    }

    /**
     * 更新文件列表（占位符方法）
     */
    updateFileList() {
        // 这个方法将在后续实现中完善
        console.log('更新文件列表:', this.fileListState);
    }

    /**
     * 刷新文件列表
     */
    refreshFileList() {
        this.showNotification('文件列表已刷新', 'success');
        this.updateFileList();
    }

    /**
     * 清空所有文件
     */
    clearAllFiles() {
        if (confirm('确定要清空所有文件吗？此操作不可撤销。')) {
            this.fileListState.totalItems = 0;
            this.updateFileList();
            this.showNotification('文件列表已清空', 'success');
        }
    }

    /**
     * 取消当前操作
     */
    cancelCurrentOperation() {
        this.hideProgress();
        this.showNotification('操作已取消', 'warning');
        this.dispatchEvent('operationCancelled');
    }
}

// ==================== 全局UI管理器实例 ====================
let uiManagerInstance = null;

/**
 * 获取全局UI管理器实例
 * @SERVICE 全局UI管理器获取函数
 * @returns {UIManager} UI管理器实例
 */
function getUIManager() {
    if (!uiManagerInstance) {
        uiManagerInstance = new UIManager();
    }
    return uiManagerInstance;
}

// 创建默认全局实例
const uiManager = getUIManager();

// 将UI管理器添加到全局作用域（兼容性）
window.uiManager = uiManager;

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['ui.js'] = {
    UIManager,
    getUIManager,
    uiManager // 兼容性导出
};
