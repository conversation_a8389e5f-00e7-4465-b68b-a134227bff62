/**
 * ==================== GoMyHire 对话分析系统 - 增强文件上传系统 ====================
 * @SERVICE 从standalone.html完整迁移的增强文件上传系统
 * 实现复杂的文件处理和历史管理，包括批量上传、进度管理、历史记录等
 */

import { formatFileSize, formatDate, generateUniqueId, safeExecute } from './utils.js';
import { StorageManager } from './storage-manager.js';

// 文件状态常量
export const FILE_STATUS = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
};

// 存储键常量
const STORAGE_KEYS = {
    FILE_REGISTRY: 'fileRegistry',
    FILE_HISTORY: 'fileHistory',
    FOLDER_PATHS: 'folderPaths'
};

// ==================== 文件注册表类 ====================
/**
 * 文件注册表类 - 负责文件的唯一标识、状态跟踪和去重处理
 * @SERVICE 文件注册管理器
 */
export class FileRegistry {
    constructor() {
        this.storage = new StorageManager();
        this.registry = this.loadRegistry();
    }

    /**
     * 加载文件注册表
     * @SERVICE 注册表加载方法
     * @returns {Object} 注册表数据
     */
    loadRegistry() {
        return this.storage.load(STORAGE_KEYS.FILE_REGISTRY, {});
    }

    /**
     * 保存文件注册表
     * @SERVICE 注册表保存方法
     * @returns {boolean} 保存是否成功
     */
    saveRegistry() {
        return this.storage.save(STORAGE_KEYS.FILE_REGISTRY, this.registry);
    }

    /**
     * 生成文件唯一标识
     * @FACTORY 文件ID生成工厂
     * @param {File} file - 文件对象
     * @returns {string} 文件唯一ID
     */
    generateFileId(file) {
        const content = `${file.name}_${file.size}_${file.lastModified}`;
        return this.simpleHash(content);
    }

    /**
     * 简单哈希函数
     * @UTIL 哈希计算工具
     * @param {string} str - 输入字符串
     * @returns {string} 哈希值
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * 注册文件
     * @SERVICE 文件注册方法
     * @param {File} file - 文件对象
     * @param {string} engine - 处理引擎
     * @returns {Object} 注册结果
     */
    registerFile(file, engine = 'AUTO') {
        const fileId = this.generateFileId(file);
        const now = new Date().toISOString();

        // 检查是否已存在
        if (this.registry[fileId]) {
            const existing = this.registry[fileId];
            if (existing.status === FILE_STATUS.COMPLETED) {
                console.log(`📋 文件已存在且已完成: ${file.name}`);
                return { fileId, isDuplicate: true, existing };
            }
        }

        // 注册新文件或更新现有文件
        this.registry[fileId] = {
            id: fileId,
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            engine: engine,
            status: FILE_STATUS.PENDING,
            createdAt: now,
            updatedAt: now,
            attempts: 0,
            error: null,
            result: null
        };

        this.saveRegistry();
        console.log(`📝 文件已注册: ${file.name} (ID: ${fileId})`);
        return { fileId, isDuplicate: false };
    }

    /**
     * 更新文件状态
     * @SERVICE 文件状态更新方法
     * @param {string} fileId - 文件ID
     * @param {string} status - 新状态
     * @param {Object} data - 额外数据
     * @returns {boolean} 更新是否成功
     */
    updateFileStatus(fileId, status, data = {}) {
        if (!this.registry[fileId]) {
            console.warn(`⚠️ 文件ID不存在: ${fileId}`);
            return false;
        }

        const file = this.registry[fileId];
        file.status = status;
        file.updatedAt = new Date().toISOString();

        // 更新额外数据
        Object.assign(file, data);

        // 如果是失败状态，增加尝试次数
        if (status === FILE_STATUS.FAILED) {
            file.attempts = (file.attempts || 0) + 1;
        }

        this.saveRegistry();
        console.log(`🔄 文件状态已更新: ${file.name} -> ${status}`);
        return true;
    }

    /**
     * 获取文件状态
     * @SERVICE 文件状态获取方法
     * @param {string} fileId - 文件ID
     * @returns {Object|null} 文件信息
     */
    getFileStatus(fileId) {
        return this.registry[fileId] || null;
    }

    /**
     * 获取所有文件状态
     * @SERVICE 所有文件获取方法
     * @returns {Array} 文件列表
     */
    getAllFiles() {
        return Object.values(this.registry);
    }

    /**
     * 获取特定状态的文件
     * @SERVICE 状态筛选方法
     * @param {string} status - 文件状态
     * @returns {Array} 筛选后的文件列表
     */
    getFilesByStatus(status) {
        return Object.values(this.registry).filter(file => file.status === status);
    }

    /**
     * 清理已完成的文件
     * @SERVICE 文件清理方法
     * @param {number} olderThanDays - 清理多少天前的文件
     * @returns {number} 清理的文件数量
     */
    cleanupCompletedFiles(olderThanDays = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

        let cleanedCount = 0;
        Object.keys(this.registry).forEach(fileId => {
            const file = this.registry[fileId];
            if (file.status === FILE_STATUS.COMPLETED &&
                new Date(file.updatedAt) < cutoffDate) {
                delete this.registry[fileId];
                cleanedCount++;
            }
        });

        if (cleanedCount > 0) {
            this.saveRegistry();
            console.log(`🧹 已清理 ${cleanedCount} 个过期文件`);
        }

        return cleanedCount;
    }

    /**
     * 重置文件状态
     * @SERVICE 文件状态重置方法
     * @param {string} fileId - 文件ID
     * @returns {boolean} 重置是否成功
     */
    resetFileStatus(fileId) {
        if (!this.registry[fileId]) {
            return false;
        }

        const file = this.registry[fileId];
        file.status = FILE_STATUS.PENDING;
        file.updatedAt = new Date().toISOString();
        file.error = null;
        file.result = null;

        this.saveRegistry();
        console.log(`🔄 文件状态已重置: ${file.name}`);
        return true;
    }

    /**
     * 删除文件
     * @SERVICE 文件删除方法
     * @param {string} fileId - 文件ID
     * @returns {boolean} 删除是否成功
     */
    removeFile(fileId) {
        if (!this.registry[fileId]) {
            console.warn(`⚠️ 文件ID不存在: ${fileId}`);
            return false;
        }
        const file = this.registry[fileId];
        delete this.registry[fileId];
        this.saveRegistry();
        console.log(`🗑️ 文件已删除: ${file.name}`);
        return true;
    }

    /**
     * 清空所有文件记录
     * @SERVICE 全部清空方法
     * @returns {boolean} 清空是否成功
     */
    clearAll() {
        this.registry = {};
        this.saveRegistry();
        console.log('🧹 所有文件记录已清空');
        return true;
    }

    /**
     * 获取统计信息
     * @SERVICE 统计信息获取方法
     * @returns {Object} 统计数据
     */
    getStats() {
        const files = Object.values(this.registry);
        const stats = {
            total: files.length,
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0,
            cancelled: 0
        };

        files.forEach(file => {
            stats[file.status] = (stats[file.status] || 0) + 1;
        });

        return stats;
    }
}

// ==================== 增强文件上传管理器类 ====================
/**
 * 增强文件上传管理器类 - 统一管理多种上传方式和文件处理
 * @COMPONENT 增强文件上传管理器
 */
export class EnhancedFileUploadManager {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.historyManager = new FileHistoryManager();
        this.fileRegistry = new FileRegistry();
        this.currentUploadType = 'single';
        this.isUploading = false;
        this.uploadQueue = [];
        this.cancelToken = null;
        this.progressCallback = null;
        this.supportedTypes = ['.txt', '.json', '.csv'];
        this.maxFileSize = 50 * 1024 * 1024; // 50MB
        this.maxBatchSize = 20;
        this.selectedFiles = new Map();
        this.folderPaths = new Map();
        this.currentPanel = 'driver-customer';
    }

    /**
     * 初始化上传管理器
     * @LIFECYCLE 管理器初始化方法
     */
    initialize() {
        this.setupEventListeners();
        this.loadFolderPaths();
        this.refreshHistoryDisplay();
        console.log('✓ 增强文件上传管理器初始化完成');
    }

    /**
     * 设置事件监听器
     * @EVENT_HANDLER 事件监听器设置方法
     */
    setupEventListeners() {
        // 上传方式切换
        document.querySelectorAll('.upload-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const uploadType = e.currentTarget.dataset.uploadType;
                this.switchUploadType(uploadType);
            });
        });

        // 文件输入变化监听
        ['single', 'multiple', 'folder'].forEach(type => {
            const input = document.getElementById(`file-input-${type}`);
            if (input) {
                input.addEventListener('change', (e) => {
                    this.handleFileSelection(e.target.files, type);
                });
            }
        });

        // 拖拽上传支持
        this.setupDragAndDrop();

        // 取消上传按钮
        const cancelBtn = document.getElementById('cancel-upload-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.cancelUpload());
        }
    }

    /**
     * 设置拖拽上传
     * @EVENT_HANDLER 拖拽上传设置方法
     */
    setupDragAndDrop() {
        const dropZones = document.querySelectorAll('.upload-drop-zone');

        dropZones.forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('drag-over');
            });

            zone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                zone.classList.remove('drag-over');
            });

            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('drag-over');

                const files = e.dataTransfer.files;
                const uploadType = this.detectUploadType(files);
                this.handleFileSelection(files, uploadType);
            });
        });
    }

    /**
     * 切换上传类型
     * @SERVICE 上传类型切换方法
     * @param {string} uploadType - 上传类型
     */
    switchUploadType(uploadType) {
        if (this.isUploading) {
            alert('正在上传中，请稍后再切换');
            return;
        }

        this.currentUploadType = uploadType;

        // 更新按钮状态
        document.querySelectorAll('.upload-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.uploadType === uploadType);
        });

        // 根据类型显示/隐藏历史预览
        const historyPreview = document.getElementById('history-preview');
        if (historyPreview) {
            historyPreview.style.display = uploadType === 'history' ? 'block' : 'none';
        }

        console.log('🔄 切换上传类型:', uploadType);
    }

    /**
     * 智能检测上传类型
     * @UTIL 上传类型检测工具
     * @param {FileList} files - 文件列表
     * @returns {string} 上传类型
     */
    detectUploadType(files) {
        if (files.length === 1) {
            return 'single';
        } else if (files.length > 1) {
            // 检查是否是文件夹上传
            const hasDirectoryStructure = Array.from(files).some(file =>
                file.webkitRelativePath && file.webkitRelativePath.includes('/')
            );
            return hasDirectoryStructure ? 'folder' : 'multiple';
        }
        return 'single';
    }

    /**
     * 处理文件选择
     * @SERVICE 文件选择处理方法
     * @param {FileList} files - 文件列表
     * @param {string} uploadType - 上传类型
     */
    async handleFileSelection(files, uploadType) {
        if (!files || files.length === 0) return;

        // 验证文件
        const validFiles = this.validateFiles(files, uploadType);
        if (validFiles.length === 0) {
            alert('没有有效的文件可以上传');
            return;
        }

        // 添加到历史记录
        const fileRecords = validFiles.map(file =>
            this.historyManager.addFileRecord(file)
        );

        // 开始上传处理
        await this.startUpload(validFiles, fileRecords, uploadType);
    }

    /**
     * 验证文件
     * @SERVICE 文件验证方法
     * @param {FileList} files - 文件列表
     * @param {string} uploadType - 上传类型
     * @returns {Array} 有效文件列表
     */
    validateFiles(files, uploadType) {
        const validFiles = [];
        const errors = [];

        Array.from(files).forEach(file => {
            // 检查文件类型
            const fileExt = '.' + file.name.split('.').pop().toLowerCase();
            if (!this.supportedTypes.includes(fileExt)) {
                errors.push(`${file.name}: 不支持的文件类型`);
                return;
            }

            // 检查文件大小
            if (file.size > this.maxFileSize) {
                errors.push(`${file.name}: 文件过大 (最大 ${this.maxFileSize / 1024 / 1024}MB)`);
                return;
            }

            // 检查批量上传数量限制
            if (uploadType === 'multiple' && validFiles.length >= this.maxBatchSize) {
                errors.push(`批量上传最多支持 ${this.maxBatchSize} 个文件`);
                return;
            }

            validFiles.push(file);
        });

        // 显示错误信息
        if (errors.length > 0) {
            console.warn('文件验证错误:', errors);
            alert('部分文件验证失败:\n' + errors.join('\n'));
        }

        return validFiles;
    }

    /**
     * 开始上传
     * @SERVICE 上传开始方法
     * @param {Array} files - 文件列表
     * @param {Array} fileRecords - 文件记录列表
     * @param {string} uploadType - 上传类型
     */
    async startUpload(files, fileRecords, uploadType) {
        if (this.isUploading) {
            alert('已有上传任务在进行中');
            return;
        }

        this.isUploading = true;
        this.uploadQueue = files;
        this.cancelToken = { cancelled: false };

        try {
            // 显示进度界面
            this.showUploadProgress(files.length, uploadType);

            // 处理文件
            const results = await this.processFiles(files, fileRecords);

            // 显示结果
            this.showUploadResults(results);

            // 刷新历史显示
            this.refreshHistoryDisplay();

            // 触发上传完成事件
            if (this.eventBus) {
                this.eventBus.emit('upload.completed', { results, uploadType });
            }

        } catch (error) {
            console.error('上传处理失败:', error);
            alert('上传处理失败: ' + error.message);
        } finally {
            this.isUploading = false;
            this.hideUploadProgress();
        }
    }

    /**
     * 处理文件
     * @SERVICE 文件处理方法
     * @param {Array} files - 文件列表
     * @param {Array} fileRecords - 文件记录列表
     * @returns {Array} 处理结果列表
     */
    async processFiles(files, fileRecords) {
        const results = [];
        const total = files.length;

        for (let i = 0; i < files.length; i++) {
            if (this.cancelToken && this.cancelToken.cancelled) {
                break;
            }

            const file = files[i];
            const record = fileRecords[i];

            try {
                // 更新状态为处理中
                this.historyManager.updateFileStatus(record.id, FILE_STATUS.PROCESSING);
                this.updateFileProgress(i + 1, total, file.name, 'processing');

                // 处理文件
                const result = await this.processFile(file);

                // 更新状态为完成
                this.historyManager.updateFileStatus(record.id, FILE_STATUS.COMPLETED, result);
                this.updateFileProgress(i + 1, total, file.name, 'completed');

                results.push({ file, result, success: true });

            } catch (error) {
                console.error(`处理文件失败: ${file.name}`, error);

                // 更新状态为失败
                this.historyManager.updateFileStatus(record.id, FILE_STATUS.FAILED, null, error.message);
                this.updateFileProgress(i + 1, total, file.name, 'failed');

                results.push({ file, error, success: false });
            }
        }

        return results;
    }

    /**
     * 处理单个文件
     * @SERVICE 单文件处理方法
     * @param {File} file - 文件对象
     * @returns {Promise<any>} 处理结果
     */
    async processFile(file) {
        try {
            // 检查全局processFile函数是否存在
            if (typeof window.processFile === 'function') {
                return await window.processFile(file, 0, 1);
            } else {
                // 降级处理：简单的文件内容读取
                console.warn('主处理函数不可用，使用降级处理模式');
                const content = await this.readFileContent(file);
                return {
                    fileName: file.name,
                    content: content,
                    processed: true,
                    timestamp: Date.now()
                };
            }
        } catch (error) {
            console.error(`文件处理失败: ${file.name}`, error);
            throw error;
        }
    }

    /**
     * 读取文件内容
     * @UTIL 文件内容读取工具
     * @param {File} file - 文件对象
     * @returns {Promise<string>} 文件内容
     */
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('文件读取失败'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    /**
     * 显示上传进度
     * @SERVICE 进度显示方法
     * @param {number} fileCount - 文件数量
     * @param {string} uploadType - 上传类型
     */
    showUploadProgress(fileCount, uploadType) {
        const uploadStatus = document.getElementById('upload-status');
        const statusTitle = document.getElementById('upload-status-title');
        const progressText = document.getElementById('upload-progress-text');

        if (uploadStatus) {
            uploadStatus.classList.remove('hidden');
        }

        if (statusTitle) {
            const typeNames = {
                single: '单文件',
                multiple: '批量文件',
                folder: '文件夹'
            };
            statusTitle.textContent = `${typeNames[uploadType] || '文件'}上传处理中...`;
        }

        this.updateOverallProgress(0, fileCount);
    }

    /**
     * 隐藏上传进度
     * @SERVICE 进度隐藏方法
     */
    hideUploadProgress() {
        const uploadStatus = document.getElementById('upload-status');
        if (uploadStatus) {
            uploadStatus.classList.add('hidden');
        }
    }

    /**
     * 更新整体进度
     * @SERVICE 整体进度更新方法
     * @param {number} current - 当前进度
     * @param {number} total - 总数
     */
    updateOverallProgress(current, total) {
        const progressFill = document.getElementById('upload-progress-fill');
        const progressText = document.getElementById('upload-progress-text');

        const percentage = total > 0 ? Math.round((current / total) * 100) : 0;

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${current}/${total} (${percentage}%)`;
        }
    }

    /**
     * 更新文件进度
     * @SERVICE 文件进度更新方法
     * @param {number} current - 当前文件索引
     * @param {number} total - 总文件数
     * @param {string} fileName - 文件名
     * @param {string} status - 处理状态
     */
    updateFileProgress(current, total, fileName, status) {
        const progressText = document.getElementById('upload-progress-text');

        // 更新整体进度
        this.updateOverallProgress(current, total);

        // 更新状态文本
        if (progressText) {
            const statusTexts = {
                processing: '处理中',
                completed: '已完成',
                failed: '失败'
            };
            progressText.textContent = `${statusTexts[status] || '处理中'}: ${fileName}`;
        }
    }

    /**
     * 显示上传结果
     * @SERVICE 结果显示方法
     * @param {Array} results - 处理结果列表
     */
    showUploadResults(results) {
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        const total = results.length;

        let message = `上传完成!\n\n`;
        message += `总计: ${total} 个文件\n`;
        message += `成功: ${successful} 个\n`;
        message += `失败: ${failed} 个\n`;

        if (failed > 0) {
            message += `\n失败文件:\n`;
            results.filter(r => !r.success).forEach(r => {
                message += `• ${r.file.name}: ${r.error.message}\n`;
            });
        }

        alert(message);
    }

    /**
     * 取消上传
     * @SERVICE 上传取消方法
     */
    cancelUpload() {
        if (this.cancelToken) {
            this.cancelToken.cancelled = true;
        }
        this.hideUploadProgress();
        this.isUploading = false;
        alert('上传已取消');
    }

    /**
     * 刷新历史显示
     * @SERVICE 历史显示刷新方法
     */
    refreshHistoryDisplay() {
        const historyStatsText = document.getElementById('history-stats-text');
        const historyListCompact = document.getElementById('history-list-compact');

        if (!historyStatsText || !historyListCompact) return;

        const history = this.historyManager.getHistory();
        const stats = this.historyManager.getStats();

        // 更新统计文本
        historyStatsText.textContent = `共 ${stats.total} 个文件`;

        // 更新历史列表（显示最近5个）
        const recentHistory = history.slice(0, 5);
        historyListCompact.innerHTML = recentHistory.map(record => {
            const statusClass = record.processingStatus;
            const statusIcon = this.getStatusIcon(record.processingStatus);
            const fileSize = formatFileSize(record.fileSize);
            const uploadTime = formatDate(record.uploadTime);

            return `
                <div class="history-item-compact">
                    <div class="history-item-icon ${statusClass}">${statusIcon}</div>
                    <div class="history-item-info">
                        <div class="history-item-name" title="${record.fileName}">${record.fileName}</div>
                        <div class="history-item-meta">${fileSize} • ${uploadTime}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * 获取状态图标
     * @UTIL 状态图标获取工具
     * @param {string} status - 状态
     * @returns {string} 图标HTML
     */
    getStatusIcon(status) {
        const icons = {
            [FILE_STATUS.PENDING]: '⏳',
            [FILE_STATUS.PROCESSING]: '🔄',
            [FILE_STATUS.COMPLETED]: '✅',
            [FILE_STATUS.FAILED]: '❌',
            [FILE_STATUS.CANCELLED]: '⏹️'
        };
        return icons[status] || '❓';
    }

    /**
     * 加载文件夹路径
     * @SERVICE 文件夹路径加载方法
     */
    loadFolderPaths() {
        try {
            const saved = localStorage.getItem(STORAGE_KEYS.FOLDER_PATHS);
            if (saved) {
                const paths = JSON.parse(saved);
                this.folderPaths = new Map(Object.entries(paths));
            }
        } catch (error) {
            console.warn('加载文件夹路径失败:', error);
        }
    }

    /**
     * 保存文件夹路径
     * @SERVICE 文件夹路径保存方法
     * @param {string} panel - 面板名称
     * @param {string} path - 文件夹路径
     */
    saveFolderPath(panel, path) {
        this.folderPaths.set(panel, path);
        localStorage.setItem(STORAGE_KEYS.FOLDER_PATHS,
            JSON.stringify(Object.fromEntries(this.folderPaths)));
    }

    /**
     * 重新处理文件
     * @SERVICE 文件重新处理方法
     * @param {string} fileId - 文件ID
     */
    async reprocessFile(fileId) {
        const record = this.historyManager.history.find(h => h.id === fileId);
        if (!record) {
            alert('文件记录不存在');
            return;
        }

        if (confirm(`确定要重新处理文件 "${record.fileName}" 吗？`)) {
            alert('由于浏览器安全限制，请重新选择并上传该文件进行处理。');
        }
    }

    /**
     * 销毁管理器
     * @LIFECYCLE 管理器销毁方法
     */
    destroy() {
        this.cancelUpload();
        this.historyManager = null;
        this.fileRegistry = null;
        console.log('🗑️ EnhancedFileUploadManager 已销毁');
    }
}

// ==================== 文件历史管理器类 ====================
/**
 * 文件历史管理器类 - 管理文件处理历史记录
 * @SERVICE 文件历史管理器
 */
export class FileHistoryManager {
    constructor() {
        this.storage = new StorageManager();
        this.history = [];
        this.maxHistorySize = 1000; // 最大历史记录数
        this.loadHistory();
    }

    /**
     * 加载历史记录
     * @SERVICE 历史记录加载方法
     */
    loadHistory() {
        this.history = this.storage.load(STORAGE_KEYS.FILE_HISTORY, []);
        console.log(`📚 已加载 ${this.history.length} 条文件历史记录`);
    }

    /**
     * 保存历史记录
     * @SERVICE 历史记录保存方法
     */
    saveHistory() {
        // 限制历史记录大小
        if (this.history.length > this.maxHistorySize) {
            this.history = this.history.slice(-this.maxHistorySize);
        }
        this.storage.save(STORAGE_KEYS.FILE_HISTORY, this.history);
    }

    /**
     * 添加文件记录
     * @SERVICE 文件记录添加方法
     * @param {Object} fileInfo - 文件信息
     * @returns {Object} 文件记录
     */
    addFileRecord(fileInfo) {
        const record = {
            id: generateUniqueId('file'),
            fileName: fileInfo.name,
            fileSize: fileInfo.size,
            fileType: fileInfo.type || this.getFileTypeFromName(fileInfo.name),
            uploadTime: Date.now(),
            processingStatus: FILE_STATUS.PENDING,
            processingResult: null,
            filePath: fileInfo.webkitRelativePath || fileInfo.name,
            lastProcessed: null,
            processCount: 0,
            errorMessage: null
        };

        // 检查是否已存在相同文件
        const existingIndex = this.history.findIndex(h =>
            h.fileName === record.fileName &&
            h.fileSize === record.fileSize
        );

        if (existingIndex >= 0) {
            // 更新现有记录
            this.history[existingIndex] = { ...this.history[existingIndex], ...record };
            console.log(`🔄 更新现有文件记录: ${record.fileName}`);
        } else {
            // 添加新记录
            this.history.push(record);
            console.log(`📝 添加新文件记录: ${record.fileName}`);
        }

        this.saveHistory();
        return existingIndex >= 0 ? this.history[existingIndex] : record;
    }

    /**
     * 更新文件处理状态
     * @SERVICE 处理状态更新方法
     * @param {string} fileId - 文件ID
     * @param {string} status - 处理状态
     * @param {any} result - 处理结果
     * @param {string} errorMessage - 错误信息
     * @returns {Object|null} 更新后的记录
     */
    updateFileStatus(fileId, status, result = null, errorMessage = null) {
        const record = this.history.find(h => h.id === fileId);
        if (record) {
            record.processingStatus = status;
            record.processingResult = result;
            record.errorMessage = errorMessage;
            record.lastProcessed = Date.now();

            if (status === FILE_STATUS.COMPLETED) {
                record.processCount++;
            }

            this.saveHistory();
            return record;
        }
        return null;
    }

    /**
     * 获取文件类型
     * @UTIL 文件类型获取工具
     * @param {string} fileName - 文件名
     * @returns {string} 文件类型
     */
    getFileTypeFromName(fileName) {
        const ext = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'txt': 'text/plain',
            'json': 'application/json',
            'csv': 'text/csv'
        };
        return typeMap[ext] || 'application/octet-stream';
    }

    /**
     * 获取历史记录
     * @SERVICE 历史记录获取方法
     * @param {Object} filters - 筛选条件
     * @returns {Array} 历史记录列表
     */
    getHistory(filters = {}) {
        let filtered = [...this.history];

        if (filters.status) {
            filtered = filtered.filter(h => h.processingStatus === filters.status);
        }

        if (filters.dateFrom) {
            filtered = filtered.filter(h => h.uploadTime >= filters.dateFrom);
        }

        if (filters.dateTo) {
            filtered = filtered.filter(h => h.uploadTime <= filters.dateTo);
        }

        return filtered.sort((a, b) => b.uploadTime - a.uploadTime);
    }

    /**
     * 移除记录
     * @SERVICE 记录移除方法
     * @param {string} fileId - 文件ID
     * @returns {boolean} 移除是否成功
     */
    removeRecord(fileId) {
        const index = this.history.findIndex(h => h.id === fileId);
        if (index >= 0) {
            this.history.splice(index, 1);
            this.saveHistory();
            console.log(`🗑️ 已移除文件记录: ${fileId}`);
            return true;
        }
        return false;
    }

    /**
     * 清空历史记录
     * @SERVICE 历史记录清空方法
     */
    clearHistory() {
        this.history = [];
        this.saveHistory();
        console.log('🧹 文件历史记录已清空');
    }

    /**
     * 获取统计信息
     * @SERVICE 历史统计获取方法
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            total: this.history.length,
            byStatus: {},
            totalSize: 0,
            avgProcessingTime: 0
        };

        this.history.forEach(record => {
            stats.byStatus[record.processingStatus] = 
                (stats.byStatus[record.processingStatus] || 0) + 1;
            stats.totalSize += record.fileSize;
        });

        return stats;
    }
}
