/**
 * ==================== GoMyHire 对话分析系统 - 标签页系统 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Tab System
 * 版本: 2.0.0
 * 功能描述: 从standalone.html完整迁移的标签页系统，实现高级标签页管理功能
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: utils.js (generateUniqueId, safeExecute)
 * 间接依赖: DOM API, CSS动画系统, History API
 * 被依赖: main.js, ui.js
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 2 (第三层，UI组件模块)
 * 加载时机: after-utils (工具模块加载后)
 * 加载条件: 依赖utils.js加载完成，DOM准备就绪
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 标签页管理器类 (TabManager类)
 *   - 数据隔离管理器 (TabDataIsolationManager类)
 *   - 标签页动画效果
 *   - 键盘快捷键支持
 *   - 标签页历史管理
 *   - 预加载机制
 * 导出接口: TabManager, TabDataIsolationManager, 常量, 初始化函数
 * 全局注册: window.ModuleExports['tabs.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 标签页切换事件、配置参数
 * 输出数据: 标签页状态变化、用户交互事件
 * 状态管理: 维护活跃标签页、历史记录
 * 事件处理: 标签页切换、加载、错误事件
 * 
 * @INTEGRATION (集成关系)
 * DOM集成: 与标签页DOM结构深度集成
 * 数据集成: 支持数据隔离和状态管理
 * 事件集成: 与全局事件系统集成
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (维护标签页状态和历史)
 * 加载性能: 快速 (预加载机制)
 * 运行时性能: 高效 (优化的动画和事件处理)
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// 标签页状态常量
const TAB_STATES = {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    LOADING: 'loading',
    ERROR: 'error',
    DISABLED: 'disabled'
};

// 动画类型常量
const ANIMATION_TYPES = {
    NONE: 'none',
    FADE: 'fade',
    SLIDE: 'slide',
    SCALE: 'scale'
};

// ==================== 标签页管理器类 ====================
/**
 * 标签页管理器类 - 负责管理所有标签页的切换、状态和生命周期
 * @COMPONENT 标签页管理器
 */
class TabManager {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            animation: options.animation || ANIMATION_TYPES.FADE,
            animationDuration: options.animationDuration || 300,
            enableHistory: options.enableHistory !== false,
            enableKeyboard: options.enableKeyboard !== false,
            enablePreload: options.enablePreload !== false,
            preloadDelay: options.preloadDelay || 500,
            onTabChange: options.onTabChange || null,
            onTabLoad: options.onTabLoad || null,
            onTabError: options.onTabError || null,
            ...options
        };

        this.tabs = new Map(); // 存储所有标签页
        this.currentTab = null; // 当前活跃标签页
        this.tabHistory = []; // 标签页历史记录
        this.isNavigating = false; // 是否正在导航中
        this.preloadedTabs = new Set(); // 已预加载的标签页

        this.initialize();
        console.log('📑 TabManager 初始化完成');
    }

    /**
     * 初始化标签页管理器
     * @INIT 标签页管理器初始化方法
     */
    initialize() {
        if (!this.container) {
            throw new Error('标签页容器未找到');
        }

        // 扫描现有标签页
        this.scanExistingTabs();

        // 设置事件监听器
        this.setupEventListeners();

        // 设置键盘导航
        if (this.options.enableKeyboard) {
            this.setupKeyboardNavigation();
        }

        // 设置预加载
        if (this.options.enablePreload) {
            this.setupPreloading();
        }

        // 激活默认标签页
        this.activateDefaultTab();
    }

    /**
     * 扫描现有标签页
     * @SERVICE 现有标签页扫描方法
     */
    scanExistingTabs() {
        const tabButtons = this.container.querySelectorAll('.tab-btn, [role="tab"]');
        const tabPanels = this.container.querySelectorAll('.tab-content, [role="tabpanel"]');

        tabButtons.forEach((button, index) => {
            const tabId = button.dataset.tab || button.getAttribute('aria-controls') || `tab-${index}`;
            const panel = document.getElementById(`${tabId}-panel`) || 
                         document.querySelector(`[aria-labelledby="${tabId}"]`) ||
                         tabPanels[index];

            if (panel) {
                this.registerTab(tabId, {
                    button: button,
                    panel: panel,
                    title: button.textContent.trim(),
                    state: button.classList.contains('active') ? TAB_STATES.ACTIVE : TAB_STATES.INACTIVE
                });

                if (button.classList.contains('active')) {
                    this.currentTab = tabId;
                }
            }
        });

        console.log(`📑 扫描到 ${this.tabs.size} 个标签页`);
    }

    /**
     * 注册标签页
     * @SERVICE 标签页注册方法
     * @param {string} tabId - 标签页ID
     * @param {Object} config - 标签页配置
     * @returns {boolean} 注册是否成功
     */
    registerTab(tabId, config) {
        if (this.tabs.has(tabId)) {
            console.warn(`标签页 ${tabId} 已存在`);
            return false;
        }

        const tabConfig = {
            id: tabId,
            title: config.title || tabId,
            button: config.button,
            panel: config.panel,
            state: config.state || TAB_STATES.INACTIVE,
            data: config.data || {},
            onActivate: config.onActivate || null,
            onDeactivate: config.onDeactivate || null,
            onLoad: config.onLoad || null,
            preload: config.preload || null,
            disabled: config.disabled || false,
            closable: config.closable || false,
            createdAt: Date.now()
        };

        this.tabs.set(tabId, tabConfig);

        // 设置ARIA属性
        this.setupTabAccessibility(tabConfig);

        console.log(`📑 标签页已注册: ${tabId}`);
        return true;
    }

    /**
     * 设置标签页无障碍属性
     * @SERVICE 标签页无障碍设置方法
     * @param {Object} tabConfig - 标签页配置
     */
    setupTabAccessibility(tabConfig) {
        const { button, panel } = tabConfig;

        if (button) {
            button.setAttribute('role', 'tab');
            button.setAttribute('aria-selected', tabConfig.state === TAB_STATES.ACTIVE);
            button.setAttribute('aria-controls', panel ? panel.id : '');
            button.setAttribute('tabindex', tabConfig.state === TAB_STATES.ACTIVE ? '0' : '-1');
        }

        if (panel) {
            panel.setAttribute('role', 'tabpanel');
            panel.setAttribute('aria-hidden', tabConfig.state !== TAB_STATES.ACTIVE);
            panel.setAttribute('aria-labelledby', button ? button.id : '');
        }
    }

    /**
     * 设置事件监听器
     * @SERVICE 事件监听器设置方法
     */
    setupEventListeners() {
        // 标签页按钮点击事件
        this.container.addEventListener('click', (e) => {
            const tabButton = e.target.closest('.tab-btn, [role="tab"]');
            if (tabButton) {
                e.preventDefault();
                const tabId = tabButton.dataset.tab || tabButton.getAttribute('aria-controls');
                if (tabId) {
                    this.switchTab(tabId);
                }
            }
        });

        // 标签页关闭事件
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-close')) {
                e.stopPropagation();
                const tabButton = e.target.closest('.tab-btn, [role="tab"]');
                if (tabButton) {
                    const tabId = tabButton.dataset.tab || tabButton.getAttribute('aria-controls');
                    if (tabId) {
                        this.closeTab(tabId);
                    }
                }
            }
        });
    }

    /**
     * 设置键盘导航
     * @SERVICE 键盘导航设置方法
     */
    setupKeyboardNavigation() {
        this.container.addEventListener('keydown', (e) => {
            const tabButton = e.target.closest('.tab-btn, [role="tab"]');
            if (!tabButton) return;

            const tabIds = Array.from(this.tabs.keys());
            const currentIndex = tabIds.indexOf(this.currentTab);

            switch (e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : tabIds.length - 1;
                    this.switchTab(tabIds[prevIndex]);
                    break;

                case 'ArrowRight':
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = currentIndex < tabIds.length - 1 ? currentIndex + 1 : 0;
                    this.switchTab(tabIds[nextIndex]);
                    break;

                case 'Home':
                    e.preventDefault();
                    this.switchTab(tabIds[0]);
                    break;

                case 'End':
                    e.preventDefault();
                    this.switchTab(tabIds[tabIds.length - 1]);
                    break;

                case 'Delete':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.closeTab(this.currentTab);
                    }
                    break;
            }
        });

        // 全局快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const tabIndex = parseInt(e.key) - 1;
                const tabIds = Array.from(this.tabs.keys());
                if (tabIds[tabIndex]) {
                    this.switchTab(tabIds[tabIndex]);
                }
            }
        });
    }

    /**
     * 设置预加载
     * @SERVICE 预加载设置方法
     */
    setupPreloading() {
        this.container.addEventListener('mouseenter', (e) => {
            const tabButton = e.target.closest('.tab-btn, [role="tab"]');
            if (tabButton) {
                const tabId = tabButton.dataset.tab || tabButton.getAttribute('aria-controls');
                if (tabId && tabId !== this.currentTab && !this.preloadedTabs.has(tabId)) {
                    setTimeout(() => {
                        this.preloadTab(tabId);
                    }, this.options.preloadDelay);
                }
            }
        }, true);
    }

    /**
     * 预加载标签页
     * @SERVICE 标签页预加载方法
     * @param {string} tabId - 标签页ID
     */
    async preloadTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || this.preloadedTabs.has(tabId)) return;

        try {
            if (tab.preload && typeof tab.preload === 'function') {
                console.log(`🔄 预加载标签页: ${tabId}`);
                await tab.preload();
                this.preloadedTabs.add(tabId);
            }
        } catch (error) {
            console.error(`预加载失败: ${tabId}`, error);
        }
    }

    /**
     * 激活默认标签页
     * @SERVICE 默认标签页激活方法
     */
    activateDefaultTab() {
        if (this.currentTab) return;

        // 查找第一个可用的标签页
        for (const [tabId, tab] of this.tabs) {
            if (!tab.disabled) {
                this.switchTab(tabId);
                break;
            }
        }
    }

    /**
     * 切换标签页
     * @SERVICE 标签页切换方法
     * @param {string} tabId - 目标标签页ID
     * @param {Object} options - 切换选项
     * @returns {Promise<boolean>} 切换是否成功
     */
    async switchTab(tabId, options = {}) {
        if (this.isNavigating) {
            console.warn('标签页切换正在进行中，请稍候');
            return false;
        }

        const targetTab = this.tabs.get(tabId);
        if (!targetTab) {
            console.error(`标签页不存在: ${tabId}`);
            return false;
        }

        if (targetTab.disabled) {
            console.warn(`标签页已禁用: ${tabId}`);
            return false;
        }

        if (tabId === this.currentTab) {
            return true;
        }

        this.isNavigating = true;

        try {
            const previousTab = this.currentTab;
            const previousTabConfig = previousTab ? this.tabs.get(previousTab) : null;

            // 记录历史
            if (this.options.enableHistory && previousTab) {
                this.tabHistory.push(previousTab);
                if (this.tabHistory.length > 10) {
                    this.tabHistory.shift();
                }
            }

            // 执行切换前回调
            if (previousTabConfig && previousTabConfig.onDeactivate) {
                const utils = getUtils();
                if (utils?.safeExecute) {
                    await utils.safeExecute(() => previousTabConfig.onDeactivate(previousTab));
                } else {
                    try {
                        await previousTabConfig.onDeactivate(previousTab);
                    } catch (error) {
                        console.error('Tab deactivation failed:', error);
                    }
                }
            }

            // 更新状态
            this.updateTabStates(previousTab, tabId);

            // 执行动画
            if (this.options.animation !== ANIMATION_TYPES.NONE) {
                await this.performAnimation(previousTabConfig, targetTab);
            }

            // 更新当前标签页
            this.currentTab = tabId;

            // 执行切换后回调
            if (targetTab.onActivate) {
                const utils2 = getUtils();
                if (utils2?.safeExecute) {
                    await utils2.safeExecute(() => targetTab.onActivate(tabId));
                } else {
                    try {
                        await targetTab.onActivate(tabId);
                    } catch (error) {
                        console.error('Tab activation failed:', error);
                    }
                }
            }

            // 触发全局回调
            if (this.options.onTabChange) {
                const utils3 = getUtils();
                if (utils3?.safeExecute) {
                    await utils3.safeExecute(() => this.options.onTabChange(tabId, previousTab));
                } else {
                    try {
                        await this.options.onTabChange(tabId, previousTab);
                    } catch (error) {
                        console.error('Tab change callback failed:', error);
                    }
                }
            }

            // 聚焦到新标签页
            if (targetTab.button) {
                targetTab.button.focus();
            }

            console.log(`📑 切换到标签页: ${tabId}`);
            return true;

        } catch (error) {
            console.error(`标签页切换失败: ${tabId}`, error);
            if (this.options.onTabError) {
                this.options.onTabError(tabId, error);
            }
            return false;
        } finally {
            this.isNavigating = false;
        }
    }

    /**
     * 更新标签页状态
     * @SERVICE 标签页状态更新方法
     * @param {string} previousTabId - 之前的标签页ID
     * @param {string} currentTabId - 当前标签页ID
     */
    updateTabStates(previousTabId, currentTabId) {
        // 更新之前标签页状态
        if (previousTabId) {
            const previousTab = this.tabs.get(previousTabId);
            if (previousTab) {
                previousTab.state = TAB_STATES.INACTIVE;
                if (previousTab.button) {
                    previousTab.button.classList.remove('active');
                    previousTab.button.setAttribute('aria-selected', 'false');
                    previousTab.button.setAttribute('tabindex', '-1');
                }
                if (previousTab.panel) {
                    previousTab.panel.classList.remove('active');
                    previousTab.panel.setAttribute('aria-hidden', 'true');
                }
            }
        }

        // 更新当前标签页状态
        const currentTab = this.tabs.get(currentTabId);
        if (currentTab) {
            currentTab.state = TAB_STATES.ACTIVE;
            if (currentTab.button) {
                currentTab.button.classList.add('active');
                currentTab.button.setAttribute('aria-selected', 'true');
                currentTab.button.setAttribute('tabindex', '0');
            }
            if (currentTab.panel) {
                currentTab.panel.classList.add('active');
                currentTab.panel.setAttribute('aria-hidden', 'false');
            }
        }
    }

    /**
     * 执行切换动画
     * @SERVICE 切换动画执行方法
     * @param {Object} previousTab - 之前的标签页配置
     * @param {Object} currentTab - 当前标签页配置
     * @returns {Promise} 动画完成Promise
     */
    async performAnimation(previousTab, currentTab) {
        const duration = this.options.animationDuration;

        switch (this.options.animation) {
            case ANIMATION_TYPES.FADE:
                return this.fadeAnimation(previousTab, currentTab, duration);
            case ANIMATION_TYPES.SLIDE:
                return this.slideAnimation(previousTab, currentTab, duration);
            case ANIMATION_TYPES.SCALE:
                return this.scaleAnimation(previousTab, currentTab, duration);
            default:
                return Promise.resolve();
        }
    }

    /**
     * 淡入淡出动画
     * @SERVICE 淡入淡出动画方法
     * @param {Object} previousTab - 之前的标签页配置
     * @param {Object} currentTab - 当前标签页配置
     * @param {number} duration - 动画时长
     * @returns {Promise} 动画完成Promise
     */
    fadeAnimation(previousTab, currentTab, duration) {
        return new Promise((resolve) => {
            if (previousTab && previousTab.panel) {
                previousTab.panel.style.transition = `opacity ${duration}ms ease`;
                previousTab.panel.style.opacity = '0';
            }

            if (currentTab.panel) {
                currentTab.panel.style.transition = `opacity ${duration}ms ease`;
                currentTab.panel.style.opacity = '0';
                
                setTimeout(() => {
                    currentTab.panel.style.opacity = '1';
                    setTimeout(resolve, duration);
                }, 50);
            } else {
                setTimeout(resolve, duration);
            }
        });
    }

    /**
     * 滑动动画
     * @SERVICE 滑动动画方法
     * @param {Object} previousTab - 之前的标签页配置
     * @param {Object} currentTab - 当前标签页配置
     * @param {number} duration - 动画时长
     * @returns {Promise} 动画完成Promise
     */
    slideAnimation(previousTab, currentTab, duration) {
        return new Promise((resolve) => {
            if (currentTab.panel) {
                currentTab.panel.style.transition = `transform ${duration}ms ease`;
                currentTab.panel.style.transform = 'translateX(100%)';
                
                requestAnimationFrame(() => {
                    currentTab.panel.style.transform = 'translateX(0)';
                    setTimeout(resolve, duration);
                });
            } else {
                setTimeout(resolve, duration);
            }
        });
    }

    /**
     * 缩放动画
     * @SERVICE 缩放动画方法
     * @param {Object} previousTab - 之前的标签页配置
     * @param {Object} currentTab - 当前标签页配置
     * @param {number} duration - 动画时长
     * @returns {Promise} 动画完成Promise
     */
    scaleAnimation(previousTab, currentTab, duration) {
        return new Promise((resolve) => {
            if (currentTab.panel) {
                currentTab.panel.style.transition = `transform ${duration}ms ease`;
                currentTab.panel.style.transform = 'scale(0.9)';
                
                requestAnimationFrame(() => {
                    currentTab.panel.style.transform = 'scale(1)';
                    setTimeout(resolve, duration);
                });
            } else {
                setTimeout(resolve, duration);
            }
        });
    }

    /**
     * 关闭标签页
     * @SERVICE 标签页关闭方法
     * @param {string} tabId - 标签页ID
     * @returns {boolean} 关闭是否成功
     */
    closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || !tab.closable) {
            return false;
        }

        // 如果是当前标签页，切换到其他标签页
        if (tabId === this.currentTab) {
            const tabIds = Array.from(this.tabs.keys()).filter(id => id !== tabId);
            if (tabIds.length > 0) {
                this.switchTab(tabIds[0]);
            }
        }

        // 移除DOM元素
        if (tab.button && tab.button.parentNode) {
            tab.button.parentNode.removeChild(tab.button);
        }
        if (tab.panel && tab.panel.parentNode) {
            tab.panel.parentNode.removeChild(tab.panel);
        }

        // 从注册表中移除
        this.tabs.delete(tabId);

        console.log(`📑 标签页已关闭: ${tabId}`);
        return true;
    }

    /**
     * 返回上一个标签页
     * @SERVICE 上一个标签页返回方法
     * @returns {boolean} 返回是否成功
     */
    goBack() {
        if (this.tabHistory.length === 0) {
            return false;
        }

        const previousTab = this.tabHistory.pop();
        return this.switchTab(previousTab);
    }

    /**
     * 获取当前标签页
     * @SERVICE 当前标签页获取方法
     * @returns {string|null} 当前标签页ID
     */
    getCurrentTab() {
        return this.currentTab;
    }

    /**
     * 获取所有标签页
     * @SERVICE 所有标签页获取方法
     * @returns {Array} 标签页列表
     */
    getAllTabs() {
        return Array.from(this.tabs.keys());
    }

    /**
     * 获取标签页配置
     * @SERVICE 标签页配置获取方法
     * @param {string} tabId - 标签页ID
     * @returns {Object|null} 标签页配置
     */
    getTabConfig(tabId) {
        return this.tabs.get(tabId) || null;
    }

    /**
     * 启用标签页
     * @SERVICE 标签页启用方法
     * @param {string} tabId - 标签页ID
     * @returns {boolean} 启用是否成功
     */
    enableTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return false;

        tab.disabled = false;
        if (tab.button) {
            tab.button.removeAttribute('disabled');
            tab.button.classList.remove('disabled');
        }

        return true;
    }

    /**
     * 禁用标签页
     * @SERVICE 标签页禁用方法
     * @param {string} tabId - 标签页ID
     * @returns {boolean} 禁用是否成功
     */
    disableTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return false;

        tab.disabled = true;
        if (tab.button) {
            tab.button.setAttribute('disabled', 'true');
            tab.button.classList.add('disabled');
        }

        // 如果是当前标签页，切换到其他标签页
        if (tabId === this.currentTab) {
            const enabledTabs = Array.from(this.tabs.entries())
                .filter(([id, config]) => !config.disabled)
                .map(([id]) => id);
            
            if (enabledTabs.length > 0) {
                this.switchTab(enabledTabs[0]);
            }
        }

        return true;
    }

    /**
     * 销毁标签页管理器
     * @LIFECYCLE 标签页管理器销毁方法
     */
    destroy() {
        this.tabs.clear();
        this.tabHistory = [];
        this.preloadedTabs.clear();
        this.currentTab = null;
        this.isNavigating = false;
        console.log('🗑️ TabManager 已销毁');
    }
}

// ==================== 标签页数据隔离管理器类 ====================
/**
 * 标签页数据隔离管理器类 - 负责管理不同标签页的数据隔离和状态管理
 * @MANAGER 标签页数据隔离管理器
 */
class TabDataIsolationManager {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.tabDataMap = new Map(); // 存储每个标签页的数据
        this.chartMap = new Map(); // 存储每个标签页的图表
        this.currentTab = 'analysis'; // 当前激活的标签页
        this.dataFilters = new Map(); // 数据过滤器
        this.lastUpdate = new Map(); // 最后更新时间

        this.setupTabDataStructure();
        this.setupDataFilters();
        console.log('🔒 TabDataIsolationManager 初始化完成');
    }

    /**
     * 设置标签页数据结构
     * @SERVICE 标签页数据结构设置方法
     */
    setupTabDataStructure() {
        // 数据分析标签页数据结构
        this.tabDataMap.set('analysis', {
            charts: {
                wordFrequency: null,
                sentimentAnalysis: null,
                problemClassification: null,
                timeDistribution: null,
                responseTime: null,
                satisfactionTrend: null
            },
            data: {
                realTimeStats: {},
                processedDialogues: [],
                analysisResults: {}
            },
            lastUpdate: null
        });

        // 详细报告标签页数据结构
        this.tabDataMap.set('reports', {
            charts: {
                overviewChart: null,
                trendChart: null,
                comparisonChart: null
            },
            data: {
                reportData: {},
                generatedReports: [],
                reportHistory: []
            },
            lastUpdate: null
        });

        // 问答题集标签页数据结构
        this.tabDataMap.set('qa-dataset', {
            charts: {
                difficultyChart: null,
                categoryChart: null,
                tagsChart: null
            },
            data: {
                qaItems: [],
                categories: {},
                tags: {},
                statistics: {}
            },
            lastUpdate: null
        });
    }

    /**
     * 设置数据过滤器
     * @SERVICE 数据过滤器设置方法
     */
    setupDataFilters() {
        // 数据分析标签页过滤器
        this.dataFilters.set('analysis', (data) => {
            return {
                wordFrequency: data.wordFrequency || {},
                questionCategories: data.questionCategories || {},
                driverStats: data.drivers || {},
                supportAgentStats: data.supportAgents || {},
                detailedQuestions: data.detailedQuestions || []
            };
        });

        // 详细报告标签页过滤器
        this.dataFilters.set('reports', (data) => {
            return {
                summaryData: this.extractSummaryData(data),
                trendData: this.extractTrendData(data),
                comparisonData: this.extractComparisonData(data)
            };
        });

        // 问答题集标签页过滤器
        this.dataFilters.set('qa-dataset', (data) => {
            return {
                qaDataset: data.qaDataset || [],
                knowledge: data.knowledge || [],
                qaCategories: data.questionCategories || {},
                qaTags: data.questionTags || {}
            };
        });
    }

    /**
     * 处理标签页切换
     * @SERVICE 标签页切换处理方法
     * @param {string} fromTab - 来源标签页
     * @param {string} toTab - 目标标签页
     */
    handleTabSwitch(fromTab, toTab) {
        const previousTab = this.currentTab;
        this.currentTab = toTab || fromTab;

        // 隐藏之前标签页的图表
        this.hideTabCharts(previousTab);

        // 显示新标签页的图表
        this.showTabCharts(this.currentTab);

        // 更新新标签页的数据
        this.refreshTabData(this.currentTab);

        console.log(`🔒 标签页数据切换: ${previousTab} -> ${this.currentTab}`);
    }

    /**
     * 隐藏标签页图表
     * @SERVICE 标签页图表隐藏方法
     * @param {string} tabId - 标签页ID
     */
    hideTabCharts(tabId) {
        const tabData = this.tabDataMap.get(tabId);
        if (!tabData) return;

        const tabPanel = document.getElementById(`${tabId}-panel`);
        if (tabPanel) {
            const chartContainers = tabPanel.querySelectorAll('.chart-container, .chart-preview');
            chartContainers.forEach(container => {
                container.style.display = 'none';
            });
        }
    }

    /**
     * 显示标签页图表
     * @SERVICE 标签页图表显示方法
     * @param {string} tabId - 标签页ID
     */
    showTabCharts(tabId) {
        const tabData = this.tabDataMap.get(tabId);
        if (!tabData) return;

        const tabPanel = document.getElementById(`${tabId}-panel`);
        if (tabPanel) {
            const chartContainers = tabPanel.querySelectorAll('.chart-container, .chart-preview');
            chartContainers.forEach(container => {
                container.style.display = 'block';
            });
        }
    }

    /**
     * 刷新标签页数据
     * @SERVICE 标签页数据刷新方法
     * @param {string} tabId - 标签页ID
     */
    refreshTabData(tabId) {
        const tabData = this.tabDataMap.get(tabId);
        if (!tabData) return;

        // 获取最新的全局数据
        const globalData = window.appDataManager?.data || {};

        // 应用过滤器
        const filter = this.dataFilters.get(tabId);
        if (filter) {
            const filteredData = filter(globalData);
            this.updateTabData(tabId, filteredData);
        }

        // 更新时间戳
        tabData.lastUpdate = Date.now();
        this.lastUpdate.set(tabId, Date.now());
    }

    /**
     * 更新标签页数据
     * @SERVICE 标签页数据更新方法
     * @param {string} tabId - 标签页ID
     * @param {Object} newData - 新数据
     */
    updateTabData(tabId, newData) {
        const tabData = this.tabDataMap.get(tabId);
        if (!tabData) return;

        // 合并新数据
        Object.assign(tabData.data, newData);
        tabData.lastUpdate = Date.now();

        // 触发数据更新事件
        if (this.eventBus) {
            this.eventBus.emit('tab.data.updated', {
                tabId,
                data: tabData.data,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取标签页数据
     * @SERVICE 标签页数据获取方法
     * @param {string} tabId - 标签页ID
     * @returns {Object|null} 标签页数据
     */
    getTabData(tabId) {
        return this.tabDataMap.get(tabId);
    }

    /**
     * 获取当前标签页数据
     * @SERVICE 当前标签页数据获取方法
     * @returns {Object|null} 当前标签页数据
     */
    getCurrentTabData() {
        return this.getTabData(this.currentTab);
    }

    /**
     * 提取摘要数据
     * @UTIL 摘要数据提取工具
     * @param {Object} data - 原始数据
     * @returns {Object} 摘要数据
     */
    extractSummaryData(data) {
        return {
            totalQuestions: (data.detailedQuestions || []).length,
            totalDrivers: Object.keys(data.drivers || {}).length,
            totalAgents: Object.keys(data.supportAgents || {}).length,
            avgSatisfaction: this.calculateAverageSatisfaction(data.detailedQuestions || [])
        };
    }

    /**
     * 提取趋势数据
     * @UTIL 趋势数据提取工具
     * @param {Object} data - 原始数据
     * @returns {Object} 趋势数据
     */
    extractTrendData(data) {
        const questions = data.detailedQuestions || [];
        const timeGroups = {};

        questions.forEach(q => {
            const date = new Date(q.timestamp || Date.now()).toDateString();
            if (!timeGroups[date]) {
                timeGroups[date] = [];
            }
            timeGroups[date].push(q);
        });

        return timeGroups;
    }

    /**
     * 提取对比数据
     * @UTIL 对比数据提取工具
     * @param {Object} data - 原始数据
     * @returns {Object} 对比数据
     */
    extractComparisonData(data) {
        const agents = data.supportAgents || {};
        const comparison = {};

        Object.entries(agents).forEach(([agentName, agentData]) => {
            comparison[agentName] = {
                totalQuestions: agentData.totalQuestions || 0,
                avgSatisfaction: agentData.avgSatisfaction || 0,
                avgEffectiveness: agentData.avgEffectiveness || 0
            };
        });

        return comparison;
    }

    /**
     * 计算平均满意度
     * @UTIL 平均满意度计算工具
     * @param {Array} questions - 问题列表
     * @returns {number} 平均满意度
     */
    calculateAverageSatisfaction(questions) {
        if (questions.length === 0) return 0;
        const total = questions.reduce((sum, q) => sum + (q.satisfaction || 0), 0);
        return total / questions.length;
    }

    /**
     * 清空标签页数据
     * @SERVICE 标签页数据清空方法
     * @param {string} tabId - 标签页ID
     */
    clearTabData(tabId) {
        const tabData = this.tabDataMap.get(tabId);
        if (tabData) {
            // 清空数据但保留结构
            Object.keys(tabData.data).forEach(key => {
                if (Array.isArray(tabData.data[key])) {
                    tabData.data[key] = [];
                } else if (typeof tabData.data[key] === 'object') {
                    tabData.data[key] = {};
                } else {
                    tabData.data[key] = null;
                }
            });

            tabData.lastUpdate = null;
            this.lastUpdate.delete(tabId);
        }
    }

    /**
     * 销毁数据隔离管理器
     * @LIFECYCLE 数据隔离管理器销毁方法
     */
    destroy() {
        this.tabDataMap.clear();
        this.chartMap.clear();
        this.dataFilters.clear();
        this.lastUpdate.clear();
        this.eventBus = null;
        console.log('🗑️ TabDataIsolationManager 已销毁');
    }
}

// ==================== 全局标签页管理器实例 ====================
let globalTabManager = null;
let globalDataIsolationManager = null;

/**
 * 获取全局标签页管理器实例
 * @SERVICE 全局标签页管理器获取函数
 * @param {string|HTMLElement} container - 容器选择器或元素
 * @param {Object} options - 配置选项
 * @returns {TabManager} 标签页管理器实例
 */
function getTabManager(container = '.tabs-container', options = {}) {
    if (!globalTabManager) {
        globalTabManager = new TabManager(container, options);
    }
    return globalTabManager;
}

/**
 * 获取全局数据隔离管理器实例
 * @SERVICE 全局数据隔离管理器获取函数
 * @param {Object} eventBus - 事件总线
 * @returns {TabDataIsolationManager} 数据隔离管理器实例
 */
function getDataIsolationManager(eventBus = null) {
    if (!globalDataIsolationManager) {
        globalDataIsolationManager = new TabDataIsolationManager(eventBus);
    }
    return globalDataIsolationManager;
}

// ==================== 便捷函数 ====================

/**
 * 初始化标签页系统
 * @SERVICE 标签页系统初始化函数
 * @param {Object} options - 初始化选项
 * @returns {Object} 初始化结果
 */
function initializeTabSystem(options = {}) {
    const container = options.container || '.tabs-container';
    const eventBus = options.eventBus || null;

    const tabManager = getTabManager(container, options);
    const dataIsolationManager = getDataIsolationManager(eventBus);

    // 绑定标签页切换事件
    tabManager.options.onTabChange = (currentTab, previousTab) => {
        dataIsolationManager.handleTabSwitch(previousTab, currentTab);

        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('tab.switched', {
            detail: { currentTab, previousTab }
        }));
    };

    console.log('✓ 标签页系统初始化完成');
    return {
        tabManager,
        dataIsolationManager,
        success: true
    };
}

/**
 * 切换标签页
 * @SERVICE 标签页切换函数
 * @param {string} tabId - 标签页ID
 * @param {Object} options - 切换选项
 * @returns {Promise<boolean>} 切换是否成功
 */
async function switchTab(tabId, options = {}) {
    const tabManager = getTabManager();
    return await tabManager.switchTab(tabId, options);
}

/**
 * 获取当前标签页
 * @SERVICE 当前标签页获取函数
 * @returns {string|null} 当前标签页ID
 */
function getCurrentTab() {
    const tabManager = getTabManager();
    return tabManager.getCurrentTab();
}

/**
 * 注册标签页
 * @SERVICE 标签页注册函数
 * @param {string} tabId - 标签页ID
 * @param {Object} config - 标签页配置
 * @returns {boolean} 注册是否成功
 */
function registerTab(tabId, config) {
    const tabManager = getTabManager();
    return tabManager.registerTab(tabId, config);
}

/**
 * 关闭标签页
 * @SERVICE 标签页关闭函数
 * @param {string} tabId - 标签页ID
 * @returns {boolean} 关闭是否成功
 */
function closeTab(tabId) {
    const tabManager = getTabManager();
    return tabManager.closeTab(tabId);
}

/**
 * 返回上一个标签页
 * @SERVICE 上一个标签页返回函数
 * @returns {boolean} 返回是否成功
 */
function goBackTab() {
    const tabManager = getTabManager();
    return tabManager.goBack();
}

// ==================== 兼容性函数 ====================

/**
 * 切换到上传标签页（兼容旧版本）
 * @SERVICE 上传标签页切换函数
 */
function switchToUploadTab() {
    const uploadTab = document.querySelector('.tab-btn[data-tab="upload"]');
    if (uploadTab) {
        uploadTab.click();
    } else {
        // 如果没有上传标签页，切换到分析标签页
        switchTab('analysis');
    }
}

/**
 * 设置标签页导航（兼容旧版本）
 * @SERVICE 标签页导航设置函数
 */
function setupTabNavigation() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');

            // 移除所有活跃状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // 激活当前标签
            btn.classList.add('active');
            const targetContent = document.getElementById(`tab-${targetTab}`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 触发标签页切换事件
            document.dispatchEvent(new CustomEvent('tab.changed', {
                detail: { tabId: targetTab }
            }));
        });
    });

    console.log('✓ 兼容性标签页导航设置完成');
}

// ==================== 全局标签页管理器实例 ====================
let tabManagerInstance = null;
let dataIsolationManagerInstance = null;

/**
 * 获取全局标签页管理器实例
 * @SERVICE 全局标签页管理器获取函数
 */
function getGlobalTabManager() {
    if (!tabManagerInstance) {
        tabManagerInstance = getTabManager();
    }
    return tabManagerInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['tabs.js'] = {
    // 类和常量
    TabManager,
    TabDataIsolationManager,
    TAB_STATES,
    ANIMATION_TYPES,
    
    // 核心函数
    getTabManager,
    getDataIsolationManager,
    getGlobalTabManager,
    initializeTabSystem,
    
    // 便捷函数
    switchTab,
    getCurrentTab,
    registerTab,
    closeTab,
    goBackTab,
    switchToUploadTab,
    setupTabNavigation
};
