/**
 * ==================== 全局常量配置模块 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Constants Configuration Module
 * 版本: 1.0.0
 * 功能描述: 提供系统全局常量定义，包括API配置、存储键、UI配置、错误消息等
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: 无 (基础模块)
 * 间接依赖: 无
 * 被依赖: utils.js, storage.js, parser.js, charts.js, main.js, ui.js, 以及所有其他模块
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 0 (第一层，基础模块)
 * 加载时机: core (核心模块，立即加载)
 * 加载条件: 无条件加载
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能: 
 *   - API端点和模型配置
 *   - 存储键定义
 *   - UI和分析配置参数  
 *   - 错误和成功消息模板
 *   - 正则表达式模式
 *   - 图表和导出配置
 * 导出接口: CONSTANTS对象 (包含所有常量分组)
 * 全局注册: window.CONSTANTS, window.ModuleExports['constants.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 无 (静态配置)
 * 输出数据: 配置常量对象，供所有模块使用
 * 状态管理: 无状态 (只读常量)
 * 事件处理: 无事件处理
 * 
 * @INTEGRATION (集成关系)
 * UI集成: 为UI组件提供颜色、尺寸、动画时长等配置
 * 服务集成: 为API调用提供端点、超时、重试配置
 * 存储集成: 定义LocalStorage键名规范
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 极小 (仅包含静态常量)
 * 加载性能: 极快 (无计算，直接定义)
 * 运行时性能: 无影响 (只读访问)
 */

const MAX_CONCURRENCY = 50;
const REQUEST_TIMEOUT = 90000;
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000;

const API_ENDPOINTS = {
    KIMI: 'https://api.moonshot.cn/v1/chat/completions',
    MODEL: 'kimi-k2-turbo-preview'
};

const STORAGE_KEYS = {
    ANALYSIS_RESULTS: 'analysisResults',
    FILE_HISTORY: 'fileHistory',
    USER_SETTINGS: 'userSettings',
    QA_DATASET: 'qaDataset',
    REPORTS: 'reports',
    LAST_SESSION: 'lastSession'
};

const FILE_CONFIG = {
    ACCEPTED_TYPES: ['.txt', '.json', '.csv'],
    MAX_FILE_SIZE: 50 * 1024 * 1024,
    ENCODING: 'UTF-8',
    BATCH_SIZE: 10
};

const UI_CONFIG = {
    PAGINATION_SIZE: 20,
    CHART_COLORS: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997', '#6c757d'],
    ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 500
};

const ANALYSIS_CONFIG = {
    MIN_MESSAGE_LENGTH: 5,
    MAX_MESSAGE_LENGTH: 1000,
    TIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
    DIFFICULTY_LEVELS: {1: '简单', 2: '一般', 3: '中等', 4: '困难', 5: '复杂'},
    SATISFACTION_LEVELS: {1: '很不满意', 2: '不满意', 3: '一般', 4: '满意', 5: '很满意'}
};

const ERROR_MESSAGES = {
    FILE_TOO_LARGE: '文件大小超过限制',
    FILE_TYPE_NOT_SUPPORTED: '不支持的文件类型',
    NETWORK_ERROR: '网络连接错误',
    API_ERROR: 'API调用失败',
    PARSE_ERROR: '文件解析失败',
    STORAGE_ERROR: '数据存储失败',
    UNKNOWN_ERROR: '未知错误'
};

const SUCCESS_MESSAGES = {
    FILE_UPLOADED: '文件上传成功',
    ANALYSIS_COMPLETE: '分析完成',
    DATA_EXPORTED: '数据导出成功',
    SETTINGS_SAVED: '设置保存成功'
};

const REGEX_PATTERNS = {
    TIME_STAMP: /(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})/,
    SPEAKER_NAME: /^([^:：]+)[：:]\s*/,
    PHONE_NUMBER: /1[3-9]\d{9}/g,
    EMAIL: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
};

const EXPORT_CONFIG = {
    CSV_DELIMITER: ',',
    CSV_ENCODING: 'UTF-8-BOM',
    DATE_FORMAT: 'YYYY-MM-DD_HH-mm-ss',
    FILE_PREFIX: 'analysis_result_'
};

const CHART_CONFIG = {
    DEFAULT_HEIGHT: 400,
    DEFAULT_WIDTH: '100%',
    COLORS: {PRIMARY: '#007bff', SUCCESS: '#28a745', WARNING: '#ffc107', DANGER: '#dc3545', INFO: '#17a2b8'},
    GRID: {LEFT: '3%', RIGHT: '4%', BOTTOM: '3%', CONTAIN_LABEL: true}
};

const APP_INFO = {
    NAME: 'GoMyHire 对话分析平台',
    VERSION: '2.0.0',
    DESCRIPTION: '司机客服对话分析系统 - 模块化版本'
};

const CONSTANTS = {
    MAX_CONCURRENCY, REQUEST_TIMEOUT, RETRY_ATTEMPTS, RETRY_DELAY,
    API_ENDPOINTS, STORAGE_KEYS, FILE_CONFIG, UI_CONFIG, ANALYSIS_CONFIG,
    ERROR_MESSAGES, SUCCESS_MESSAGES, REGEX_PATTERNS, EXPORT_CONFIG,
    CHART_CONFIG, APP_INFO
};

window.CONSTANTS = CONSTANTS;

// 模块导出
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['constants.js'] = CONSTANTS;
