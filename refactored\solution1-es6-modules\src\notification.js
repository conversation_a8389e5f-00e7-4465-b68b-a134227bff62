/**
 * ==================== GoMyHire 对话分析系统 - 通知系统 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Notification System
 * 版本: 2.0.0
 * 功能描述: 从standalone.html完整迁移的通知系统，实现各类消息通知和用户反馈
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: utils.js (generateUniqueId, safeExecute)
 * 间接依赖: DOM API, CSS动画系统
 * 被依赖: main.js, ui.js, 各类业务模块
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 2 (第三层，UI组件模块)
 * 加载时机: after-utils (工具模块加载后)
 * 加载条件: 依赖utils.js加载完成，DOM准备就绪
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 通知管理器类 (NotificationManager类)
 *   - 多种通知类型 (SUCCESS, ERROR, WARNING, INFO, LOADING)
 *   - 灵活的位置配置
 *   - 动画效果和生命周期管理
 *   - 快捷函数 (showSuccess, showError, showWarning, showInfo)
 * 导出接口: NotificationManager, 通知类型常量, 快捷函数
 * 全局注册: window.ModuleExports['notification.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 通知消息、类型、配置参数
 * 输出数据: 可视化通知组件、用户交互事件
 * 状态管理: 维护活跃通知列表、动画状态
 * 事件处理: 通知点击、关闭事件
 * 
 * @INTEGRATION (集成关系)
 * DOM集成: 动态创建和管理通知DOM元素
 * CSS集成: 内置样式和动画效果
 * 事件集成: 全局事件系统集成
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 低 (轻量级组件)
 * 加载性能: 快速 (无重外部依赖)
 * 运行时性能: 高效 (优化的DOM操作和动画)
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// 通知类型常量
const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info',
    LOADING: 'loading'
};

// 通知位置常量
const NOTIFICATION_POSITIONS = {
    TOP_RIGHT: 'top-right',
    TOP_LEFT: 'top-left',
    TOP_CENTER: 'top-center',
    BOTTOM_RIGHT: 'bottom-right',
    BOTTOM_LEFT: 'bottom-left',
    BOTTOM_CENTER: 'bottom-center'
};

// ==================== 通知管理器类 ====================
/**
 * 通知管理器类 - 负责管理所有通知的显示、隐藏和生命周期
 * @COMPONENT 通知管理器
 */
class NotificationManager {
    constructor() {
        this.notifications = new Map(); // 存储所有活跃的通知
        this.container = null; // 通知容器
        this.maxNotifications = 5; // 最大同时显示的通知数量
        this.defaultDuration = 5000; // 默认显示时长（毫秒）
        this.defaultPosition = NOTIFICATION_POSITIONS.TOP_RIGHT; // 默认位置
        this.zIndexBase = 9999; // 基础z-index值
        
        this.initializeContainer();
        console.log('📢 NotificationManager 初始化完成');
    }

    /**
     * 初始化通知容器
     * @INIT 通知容器初始化方法
     */
    initializeContainer() {
        // 创建通知容器
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: ${this.zIndexBase};
            pointer-events: none;
            max-width: 400px;
        `;

        document.body.appendChild(this.container);
    }

    /**
     * 显示通知
     * @SERVICE 通知显示方法
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    show(message, options = {}) {
        const config = {
            type: options.type || NOTIFICATION_TYPES.INFO,
            duration: options.duration !== undefined ? options.duration : this.defaultDuration,
            position: options.position || this.defaultPosition,
            closable: options.closable !== false,
            persistent: options.persistent === true,
            onClick: options.onClick || null,
            onClose: options.onClose || null,
            title: options.title || null,
            icon: options.icon || null,
            actions: options.actions || []
        };

        // 生成唯一ID
        const utils = getUtils();\n        const notificationId = utils?.generateUniqueId('notification') || 'notification_' + Date.now();

        // 检查通知数量限制
        this.enforceMaxNotifications();

        // 创建通知元素
        const notificationElement = this.createNotificationElement(message, config);
        
        // 存储通知信息
        this.notifications.set(notificationId, {
            id: notificationId,
            element: notificationElement,
            config: config,
            createdAt: Date.now()
        });

        // 添加到容器
        this.container.appendChild(notificationElement);

        // 应用动画
        this.animateIn(notificationElement);

        // 设置自动关闭
        if (!config.persistent && config.duration > 0) {
            setTimeout(() => {
                this.hide(notificationId);
            }, config.duration);
        }

        console.log(`📢 通知已显示: ${notificationId} - ${message}`);
        return notificationId;
    }

    /**
     * 创建通知元素
     * @SERVICE 通知元素创建方法
     * @param {string} message - 通知消息
     * @param {Object} config - 通知配置
     * @returns {HTMLElement} 通知元素
     */
    createNotificationElement(message, config) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${config.type}`;
        
        // 基础样式
        notification.style.cssText = `
            background: ${this.getBackgroundColor(config.type)};
            color: ${this.getTextColor(config.type)};
            border: 1px solid ${this.getBorderColor(config.type)};
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-size: 14px;
            line-height: 1.4;
            pointer-events: auto;
            position: relative;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 100%;
            word-wrap: break-word;
        `;

        // 构建内容
        let content = '';

        // 添加图标
        const icon = config.icon || this.getDefaultIcon(config.type);
        if (icon) {
            content += `<i class="notification-icon ${icon}" style="margin-right: 8px;"></i>`;
        }

        // 添加标题
        if (config.title) {
            content += `<div class="notification-title" style="font-weight: bold; margin-bottom: 4px;">${config.title}</div>`;
        }

        // 添加消息
        content += `<div class="notification-message">${message}</div>`;

        // 添加操作按钮
        if (config.actions && config.actions.length > 0) {
            content += '<div class="notification-actions" style="margin-top: 12px;">';
            config.actions.forEach(action => {
                content += `<button class="notification-action-btn" data-action="${action.key}" style="
                    background: transparent;
                    border: 1px solid currentColor;
                    color: inherit;
                    padding: 4px 12px;
                    margin-right: 8px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                ">${action.label}</button>`;
            });
            content += '</div>';
        }

        // 添加关闭按钮
        if (config.closable) {
            content += `<button class="notification-close" style="
                position: absolute;
                top: 8px;
                right: 8px;
                background: none;
                border: none;
                color: inherit;
                cursor: pointer;
                font-size: 18px;
                line-height: 1;
                opacity: 0.7;
                padding: 0;
                width: 20px;
                height: 20px;
            ">&times;</button>`;
        }

        notification.innerHTML = content;

        // 绑定事件
        this.bindNotificationEvents(notification, config);

        return notification;
    }

    /**
     * 绑定通知事件
     * @SERVICE 通知事件绑定方法
     * @param {HTMLElement} element - 通知元素
     * @param {Object} config - 通知配置
     */
    bindNotificationEvents(element, config) {
        // 关闭按钮事件
        const closeBtn = element.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const notificationId = this.findNotificationId(element);
                if (notificationId) {
                    this.hide(notificationId);
                }
            });
        }

        // 操作按钮事件
        const actionBtns = element.querySelectorAll('.notification-action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const actionKey = btn.dataset.action;
                const action = config.actions.find(a => a.key === actionKey);
                if (action && action.handler) {
                    const notificationId = this.findNotificationId(element);
                    action.handler(notificationId);
                }
            });
        });

        // 点击事件
        if (config.onClick) {
            element.addEventListener('click', () => {
                const notificationId = this.findNotificationId(element);
                config.onClick(notificationId);
            });
            element.style.cursor = 'pointer';
        }
    }

    /**
     * 查找通知ID
     * @UTIL 通知ID查找工具
     * @param {HTMLElement} element - 通知元素
     * @returns {string|null} 通知ID
     */
    findNotificationId(element) {
        for (const [id, notification] of this.notifications) {
            if (notification.element === element) {
                return id;
            }
        }
        return null;
    }

    /**
     * 隐藏通知
     * @SERVICE 通知隐藏方法
     * @param {string} notificationId - 通知ID
     * @returns {boolean} 隐藏是否成功
     */
    hide(notificationId) {
        const notification = this.notifications.get(notificationId);
        if (!notification) {
            return false;
        }

        // 执行关闭回调
        if (notification.config.onClose) {
            safeExecute(() => notification.config.onClose(notificationId));
        }

        // 应用退出动画
        this.animateOut(notification.element, () => {
            // 从DOM中移除
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            
            // 从存储中移除
            this.notifications.delete(notificationId);
            
            console.log(`📢 通知已隐藏: ${notificationId}`);
        });

        return true;
    }

    /**
     * 入场动画
     * @SERVICE 入场动画方法
     * @param {HTMLElement} element - 通知元素
     */
    animateIn(element) {
        requestAnimationFrame(() => {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        });
    }

    /**
     * 退场动画
     * @SERVICE 退场动画方法
     * @param {HTMLElement} element - 通知元素
     * @param {Function} callback - 动画完成回调
     */
    animateOut(element, callback) {
        element.style.transform = 'translateX(100%)';
        element.style.opacity = '0';
        
        setTimeout(() => {
            if (callback) callback();
        }, 300);
    }

    /**
     * 获取背景颜色
     * @UTIL 背景颜色获取工具
     * @param {string} type - 通知类型
     * @returns {string} 背景颜色
     */
    getBackgroundColor(type) {
        const colors = {
            [NOTIFICATION_TYPES.SUCCESS]: '#f0fdf4',
            [NOTIFICATION_TYPES.ERROR]: '#fef2f2',
            [NOTIFICATION_TYPES.WARNING]: '#fffbeb',
            [NOTIFICATION_TYPES.INFO]: '#eff6ff',
            [NOTIFICATION_TYPES.LOADING]: '#f8fafc'
        };
        return colors[type] || colors[NOTIFICATION_TYPES.INFO];
    }

    /**
     * 获取文本颜色
     * @UTIL 文本颜色获取工具
     * @param {string} type - 通知类型
     * @returns {string} 文本颜色
     */
    getTextColor(type) {
        const colors = {
            [NOTIFICATION_TYPES.SUCCESS]: '#16a34a',
            [NOTIFICATION_TYPES.ERROR]: '#dc2626',
            [NOTIFICATION_TYPES.WARNING]: '#d97706',
            [NOTIFICATION_TYPES.INFO]: '#2563eb',
            [NOTIFICATION_TYPES.LOADING]: '#64748b'
        };
        return colors[type] || colors[NOTIFICATION_TYPES.INFO];
    }

    /**
     * 获取边框颜色
     * @UTIL 边框颜色获取工具
     * @param {string} type - 通知类型
     * @returns {string} 边框颜色
     */
    getBorderColor(type) {
        const colors = {
            [NOTIFICATION_TYPES.SUCCESS]: '#bbf7d0',
            [NOTIFICATION_TYPES.ERROR]: '#fecaca',
            [NOTIFICATION_TYPES.WARNING]: '#fed7aa',
            [NOTIFICATION_TYPES.INFO]: '#bfdbfe',
            [NOTIFICATION_TYPES.LOADING]: '#cbd5e1'
        };
        return colors[type] || colors[NOTIFICATION_TYPES.INFO];
    }

    /**
     * 获取默认图标
     * @UTIL 默认图标获取工具
     * @param {string} type - 通知类型
     * @returns {string} 图标类名
     */
    getDefaultIcon(type) {
        const icons = {
            [NOTIFICATION_TYPES.SUCCESS]: 'fas fa-check-circle',
            [NOTIFICATION_TYPES.ERROR]: 'fas fa-exclamation-circle',
            [NOTIFICATION_TYPES.WARNING]: 'fas fa-exclamation-triangle',
            [NOTIFICATION_TYPES.INFO]: 'fas fa-info-circle',
            [NOTIFICATION_TYPES.LOADING]: 'fas fa-spinner fa-spin'
        };
        return icons[type] || icons[NOTIFICATION_TYPES.INFO];
    }

    /**
     * 强制执行最大通知数量限制
     * @SERVICE 通知数量限制方法
     */
    enforceMaxNotifications() {
        if (this.notifications.size >= this.maxNotifications) {
            // 移除最旧的通知
            const oldestId = Array.from(this.notifications.keys())[0];
            this.hide(oldestId);
        }
    }

    /**
     * 清空所有通知
     * @SERVICE 通知清空方法
     */
    clearAll() {
        const notificationIds = Array.from(this.notifications.keys());
        notificationIds.forEach(id => this.hide(id));
    }

    /**
     * 获取活跃通知数量
     * @SERVICE 活跃通知数量获取方法
     * @returns {number} 活跃通知数量
     */
    getActiveCount() {
        return this.notifications.size;
    }

    /**
     * 设置最大通知数量
     * @SERVICE 最大通知数量设置方法
     * @param {number} max - 最大数量
     */
    setMaxNotifications(max) {
        this.maxNotifications = Math.max(1, max);
        this.enforceMaxNotifications();
    }

    /**
     * 销毁通知管理器
     * @LIFECYCLE 通知管理器销毁方法
     */
    destroy() {
        this.clearAll();
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        this.notifications.clear();
        console.log('🗑️ NotificationManager 已销毁');
    }

    // ==================== 便捷方法 ====================

    /**
     * 显示成功通知
     * @SERVICE 成功通知显示方法
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    success(message, options = {}) {
        return this.show(message, {
            ...options,
            type: NOTIFICATION_TYPES.SUCCESS
        });
    }

    /**
     * 显示错误通知
     * @SERVICE 错误通知显示方法
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    error(message, options = {}) {
        return this.show(message, {
            ...options,
            type: NOTIFICATION_TYPES.ERROR,
            duration: options.duration !== undefined ? options.duration : 8000 // 错误通知显示更久
        });
    }

    /**
     * 显示警告通知
     * @SERVICE 警告通知显示方法
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    warning(message, options = {}) {
        return this.show(message, {
            ...options,
            type: NOTIFICATION_TYPES.WARNING
        });
    }

    /**
     * 显示信息通知
     * @SERVICE 信息通知显示方法
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    info(message, options = {}) {
        return this.show(message, {
            ...options,
            type: NOTIFICATION_TYPES.INFO
        });
    }

    /**
     * 显示加载通知
     * @SERVICE 加载通知显示方法
     * @param {string} message - 通知消息
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    loading(message, options = {}) {
        return this.show(message, {
            ...options,
            type: NOTIFICATION_TYPES.LOADING,
            persistent: true, // 加载通知默认持久显示
            closable: false // 加载通知默认不可关闭
        });
    }

    /**
     * 显示确认通知
     * @SERVICE 确认通知显示方法
     * @param {string} message - 通知消息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    confirm(message, onConfirm, onCancel, options = {}) {
        return this.show(message, {
            ...options,
            type: NOTIFICATION_TYPES.WARNING,
            persistent: true,
            actions: [
                {
                    key: 'confirm',
                    label: '确认',
                    handler: (notificationId) => {
                        this.hide(notificationId);
                        if (onConfirm) onConfirm();
                    }
                },
                {
                    key: 'cancel',
                    label: '取消',
                    handler: (notificationId) => {
                        this.hide(notificationId);
                        if (onCancel) onCancel();
                    }
                }
            ]
        });
    }

    /**
     * 显示进度通知
     * @SERVICE 进度通知显示方法
     * @param {string} message - 通知消息
     * @param {number} progress - 进度百分比 (0-100)
     * @param {Object} options - 通知选项
     * @returns {string} 通知ID
     */
    progress(message, progress = 0, options = {}) {
        const progressBar = `
            <div class="notification-progress" style="margin-top: 8px;">
                <div class="progress-bar" style="
                    width: 100%;
                    height: 4px;
                    background: rgba(255,255,255,0.3);
                    border-radius: 2px;
                    overflow: hidden;
                ">
                    <div class="progress-fill" style="
                        width: ${Math.max(0, Math.min(100, progress))}%;
                        height: 100%;
                        background: currentColor;
                        transition: width 0.3s ease;
                    "></div>
                </div>
                <div class="progress-text" style="
                    font-size: 12px;
                    margin-top: 4px;
                    opacity: 0.8;
                ">${Math.round(progress)}%</div>
            </div>
        `;

        return this.show(message + progressBar, {
            ...options,
            type: NOTIFICATION_TYPES.INFO,
            persistent: true,
            closable: false
        });
    }

    /**
     * 更新进度通知
     * @SERVICE 进度通知更新方法
     * @param {string} notificationId - 通知ID
     * @param {string} message - 新消息
     * @param {number} progress - 新进度百分比
     */
    updateProgress(notificationId, message, progress) {
        const notification = this.notifications.get(notificationId);
        if (!notification) return;

        const progressFill = notification.element.querySelector('.progress-fill');
        const progressText = notification.element.querySelector('.progress-text');
        const messageEl = notification.element.querySelector('.notification-message');

        if (progressFill) {
            progressFill.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }

        if (progressText) {
            progressText.textContent = `${Math.round(progress)}%`;
        }

        if (messageEl && message) {
            // 更新消息，保留进度条
            const progressBar = notification.element.querySelector('.notification-progress');
            messageEl.innerHTML = message;
            if (progressBar) {
                messageEl.appendChild(progressBar);
            }
        }
    }
}

// ==================== 全局通知管理器实例 ====================
let globalNotificationManager = null;

/**
 * 获取全局通知管理器实例
 * @SERVICE 全局通知管理器获取函数
 * @returns {NotificationManager} 通知管理器实例
 */
function getNotificationManager() {
    if (!globalNotificationManager) {
        globalNotificationManager = new NotificationManager();
    }
    return globalNotificationManager;
}

// ==================== 便捷函数 ====================

/**
 * 显示成功通知
 * @SERVICE 成功通知显示函数
 * @param {string} message - 通知消息
 * @param {Object} options - 通知选项
 * @returns {string} 通知ID
 */
function showSuccess(message, options = {}) {
    return getNotificationManager().success(message, options);
}

/**
 * 显示错误通知
 * @SERVICE 错误通知显示函数
 * @param {string} message - 通知消息
 * @param {Object} options - 通知选项
 * @returns {string} 通知ID
 */
function showError(message, options = {}) {
    return getNotificationManager().error(message, options);
}

/**
 * 显示警告通知
 * @SERVICE 警告通知显示函数
 * @param {string} message - 通知消息
 * @param {Object} options - 通知选项
 * @returns {string} 通知ID
 */
function showWarning(message, options = {}) {
    return getNotificationManager().warning(message, options);
}

/**
 * 显示信息通知
 * @SERVICE 信息通知显示函数
 * @param {string} message - 通知消息
 * @param {Object} options - 通知选项
 * @returns {string} 通知ID
 */
function showInfo(message, options = {}) {
    return getNotificationManager().info(message, options);
}

/**
 * 显示加载通知
 * @SERVICE 加载通知显示函数
 * @param {string} message - 通知消息
 * @param {Object} options - 通知选项
 * @returns {string} 通知ID
 */
function showLoading(message, options = {}) {
    return getNotificationManager().loading(message, options);
}

/**
 * 隐藏通知
 * @SERVICE 通知隐藏函数
 * @param {string} notificationId - 通知ID
 * @returns {boolean} 隐藏是否成功
 */
function hideNotification(notificationId) {
    return getNotificationManager().hide(notificationId);
}

/**
 * 清空所有通知
 * @SERVICE 通知清空函数
 */
function clearAllNotifications() {
    getNotificationManager().clearAll();
}

// ==================== 兼容性函数 ====================

/**
 * 显示成功消息（兼容旧版本）
 * @SERVICE 成功消息显示函数
 * @param {string} message - 消息内容
 * @param {string} fileName - 文件名（可选）
 */
function showSuccessToUser(message, fileName = '') {
    const fullMessage = fileName ? `文件: ${fileName}\n${message}` : message;
    return showSuccess(fullMessage, {
        title: '处理成功',
        duration: 5000
    });
}

/**
 * 显示错误消息（兼容旧版本）
 * @SERVICE 错误消息显示函数
 * @param {string} message - 消息内容
 * @param {string} fileName - 文件名（可选）
 */
function showErrorToUser(message, fileName = '') {
    const fullMessage = fileName ? `文件: ${fileName}\n${message}` : message;
    return showError(fullMessage, {
        title: '处理失败',
        duration: 8000
    });
}

/**
 * 显示优化通知
 * @SERVICE 优化通知显示函数
 * @param {number} count - 优化数量
 */
function showOptimizationNotification(count) {
    return showInfo(`发现 ${count} 个可优化的问答条目`, {
        title: '优化建议',
        duration: 6000,
        actions: [
            {
                key: 'optimize',
                label: '立即优化',
                handler: () => {
                    // 触发优化事件
                    document.dispatchEvent(new CustomEvent('optimization.start'));
                }
            }
        ]
    });
}

/**
 * 显示优化结果
 * @SERVICE 优化结果显示函数
 * @param {Object} result - 优化结果
 */
function showOptimizationResult(result) {
    const message = `优化完成！从 ${result.original.count} 条优化到 ${result.optimized.count} 条，减少了 ${result.statistics.reductionRate}%`;

    return showSuccess(message, {
        title: '优化完成',
        duration: 8000,
        actions: [
            {
                key: 'view',
                label: '查看详情',
                handler: () => {
                    // 触发查看详情事件
                    document.dispatchEvent(new CustomEvent('optimization.view', { detail: result }));
                }
            }
        ]
    });
}

/**
 * 显示消息提示（兼容旧版本）
 * @SERVICE 消息提示显示函数
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型
 */
function showMessage(message, type = 'info') {
    const typeMap = {
        'success': NOTIFICATION_TYPES.SUCCESS,
        'error': NOTIFICATION_TYPES.ERROR,
        'warning': NOTIFICATION_TYPES.WARNING,
        'info': NOTIFICATION_TYPES.INFO
    };

    return getNotificationManager().show(message, {
        type: typeMap[type] || NOTIFICATION_TYPES.INFO,
        duration: 4000
    });
}

// ==================== 全局通知管理器实例 ====================
let notificationManagerInstance = null;

/**
 * 获取全局通知管理器实例
 * @SERVICE 全局通知管理器获取函数  
 * @returns {NotificationManager} 通知管理器实例
 */
function getGlobalNotificationManager() {
    if (!notificationManagerInstance) {
        notificationManagerInstance = getNotificationManager();
    }
    return notificationManagerInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['notification.js'] = {
    // 类和常量
    NotificationManager,
    NOTIFICATION_TYPES,
    NOTIFICATION_POSITIONS,
    
    // 核心函数
    getNotificationManager,
    getGlobalNotificationManager,
    
    // 便捷函数
    showSuccess,
    showError, 
    showWarning,
    showInfo,
    showLoading,
    hideNotification,
    clearAllNotifications,
    
    // 用户友好函数
    showSuccessToUser,
    showErrorToUser,
    showOptimizationNotification,
    showOptimizationResult,
    showMessage
};
